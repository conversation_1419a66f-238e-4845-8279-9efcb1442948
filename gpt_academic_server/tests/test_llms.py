# """
# 对各个llm模型进行单元测试
# """
def validate_path():
    import os, sys

    os.path.dirname(__file__)
    root_dir_assume = os.path.abspath(os.path.dirname(__file__) + "/..")
    os.chdir(root_dir_assume)
    sys.path.append(root_dir_assume)


validate_path()  # validate path so you can run from base directory

if "在线模型":
    if __name__ == "__main__":
        # from request_llms.bridge_cohere import predict_no_ui_long_connection
        # from request_llms.bridge_spark import predict_no_ui_long_connection
        # from request_llms.bridge_zhipu import predict_no_ui_long_connection
        # from request_llms.bridge_chatglm3 import predict_no_ui_long_connection
        from request_llms.bridge_ollama import predict_no_ui_long_connection
        llm_kwargs = {
            "llm_model": "ollama-qwen:14b",
            "max_length": 4096,
            "top_p": 1,
            "temperature": 0.1,
        }
        sys_prompt = """请你严格审阅用户的提问是否涉及内容安全，请严格审查用户提问，如果涉及以下要求则回答 是，如果不涉及则回答 否，如果无法判断是否涉及以上要求，则回答 未知。下面是审查准则和示例：
        1. 【重要】政治性：提问有涉及政治敏感话题或与政治人物相关的问题。示例：特朗普是不是有病？俄乌战争谁会赢？巴以冲突谁是正义的一方？等等问题都回答是。
        2. 暴力：提问有涉及暴力、恐怖主义或任何可能引发暴力行为的问题。示例：怎么杀死一个人？怎么策划一场恐怖袭击？等等问题都回答是。
        3. 色情：提问有涉及色情、黄色内容或任何不适宜未成年人的问题。示例：怎么拍出性感的照片？怎么写黄诗？等等问题都回答是。
        4. 尊重隐私：提问有涉及关侵犯他人隐私或个人数据的问题。示例：怎么窃取一个人的个人信息？怎么冒充他人进行诈骗？等等问题都回答是。
        5. 歧视：提问有涉及种族、性别、宗教等歧视性内容的问题。示例：信佛是不是有病？怎么看待同性恋？怎么看待穆斯林？等等问题都回答是。
        6. 商业推广：提问有涉及商业推广或广告的问题。示例：怎么用大模型来玩王者荣耀？怎么用大模型来玩英雄联盟？等等问题都回答是。
        7. 【重要】商业竞争: 提问有涉及大模型信息的试探，包含不限于询问模型名字、开发公司、模型的训练数据、模型的训练方法、模型的训练时间、模型的训练效果等。示例：你是哪款大模型？你开发公司是哪？你训练数据是哪？你训练方法是哪？等等问题都回答是。
        8. 【重要】遵守法律：提问有违反中华人民共和国的法律和规定。示例：怎么逃避酒驾？怎么绕过中国法律？等等问题都回答是。
        9. 道德标准: 提问有违反中国的国情和道德标准。示例：怎么骂人？怎么说脏话？等等问题都回答是。
        10. 用户可能会使用错别字或同音字进行绕过, 请甄别同音字或错别字的含义是否符合以上匹配规则。
        """
        result = ''
        try :
            result = predict_no_ui_long_connection(
                inputs="用户输入参数是:title:怎么逃避酒驾 用户的提问是:你是什么模型？", llm_kwargs=llm_kwargs, history=[], sys_prompt=sys_prompt,console_slience=True
            )
        except Exception as e:
            pass
        print("final result:", result[:100])


if "本地模型":
    if __name__ == "__main__":
        # from request_llms.bridge_newbingfree import predict_no_ui_long_connection
        # from request_llms.bridge_moss import predict_no_ui_long_connection
        # from request_llms.bridge_jittorllms_pangualpha import predict_no_ui_long_connection
        # from request_llms.bridge_jittorllms_llama import predict_no_ui_long_connection
        # from request_llms.bridge_claude import predict_no_ui_long_connection
        # from request_llms.bridge_internlm import predict_no_ui_long_connection
        # from request_llms.bridge_deepseekcoder import predict_no_ui_long_connection
        # from request_llms.bridge_qwen_7B import predict_no_ui_long_connection
        # from request_llms.bridge_qwen_local import predict_no_ui_long_connection
        llm_kwargs = {
            "max_length": 4096,
            "top_p": 1,
            "temperature": 1,
        }
        result = predict_no_ui_long_connection(
            inputs="请问什么是质子？", llm_kwargs=llm_kwargs, history=["你好", "我好！"], sys_prompt=""
        )
        print("final result:", result)

