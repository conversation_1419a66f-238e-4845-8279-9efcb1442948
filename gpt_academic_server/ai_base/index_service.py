from datetime import datetime
from loguru import logger
import json
import requests
from toolbox import get_conf
AI_ENV, AI_BASE_URL, AI_CHECK_TOKEN_URL, AI_GRAPHQL_URL, AI_EXT_API_KEY = get_conf('AI_ENV', 'AI_BASE_URL', 'AI_CHECK_TOKEN_URL', 'AI_GRAPHQL_URL', 'AI_EXT_API_KEY')
from request_llms.bridge_ollama import predict_no_ui_long_connection
#from request_llms.bridge_qwen import predict_no_ui_long_connection

class IndexService:
    def test(self, name: str):
        return [{"item_id": name}, {"item_id": "Bar"}]
    
    def check_token(self, token: str)->bool:
        """
        检查给定的令牌是否有效。

        参数:
        - token (str): 需要检查的令牌字符串。

        返回值:
        - 如果令牌有效，返回令牌的相关数据；如果无效或出错，返回False。
        """

        url = AI_BASE_URL + AI_CHECK_TOKEN_URL + '?token=' + token
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Tenant-Id': 1
        }

        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return False

        response_json = response.json()
        if response_json.get('code', 500) == 0:
            res = response_json.get('data', False)
            return res

        return False
    
    def send_to_hasura(self, token:str, query:str, variables:dict)->dict:
        """
        向 Hasura 发送 GraphQL 查询请求

        参数:
        - token (str): 用于授权的令牌
        - query (str): GraphQL 查询字符串
        - variables (dict): GraphQL 查询中使用的变量

        返回值:
        - response.json(): 当查询成功且有数据返回时，返回 JSON 格式的数据
        - Exception: 当查询失败或无数据时抛出异常，包含错误信息或详细响应内容
        """

        url = AI_BASE_URL + AI_GRAPHQL_URL
        headers = {
            'Content-Type': 'application/json',
            'Authorization': token,
            'X-Hasura-Role': 'tenant_admin',
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)'
        }
        json_param = {
            'query': query,
            'variables': variables
        }

        response = requests.post(url, json=json_param, headers=headers)

        if AI_ENV != 'prod':
            # 测试环境打印请求信息
            logger.warning(json.dumps(headers)+json.dumps(json_param)+json.dumps(response.json()))

        if response.status_code == 200:
            if response.json().get('data') is not None :
                return response.json().get('data')
            else :
                logger.warning(json.dumps(headers)+json.dumps(json_param)+json.dumps(response.json()))
                raise Exception("操作失败")
        else :
            raise Exception("未查询到数据")
        
    def content_review(self, data: dict, authorization: str) :
        """
        内容审核
        参考 https://docs.dify.ai/v/zh-hans/guides/extension/api_based_extension/moderation
        报错503参考 https://github.com/langgenius/dify/issues/4581
        """
        auth_scheme, _, api_key = authorization.partition(' ')

        if auth_scheme.lower() != "bearer" or api_key != AI_EXT_API_KEY:
            raise Exception(status_code=401, detail="Unauthorized")

        point = data.point

        # for debug
        logger.info(f"point: {data}, api_key: {authorization}")

        if point == "ping":
            return {
                "result": "pong"
            }
        if point == "app.moderation.input":
            return self.handle_moderation_input(params=data.params)
        if point == "app.moderation.output":
            return self.handle_moderation_output(params=data.params)
        
    def handle_moderation_input(self, params: dict):
        # 可以分不同的app_id处理
        logger.info("handle_moderation_input")
        inputs = ""
        if isinstance(params.get("inputs"), dict) and len(params.get("inputs")) > 0:
            inputs = "用户输入参数是:" + str(params.get("inputs"))

        if params.get("query") is not None and params.get("query") != "" :
            inputs = inputs + " 用户的提问是:" + params.get("query")

        # 审查
        flagged = True
        if inputs == "": 
            flagged = False
        else :
            flagged = self.get_flagged(inputs = inputs)

        res = {
            "flagged": flagged,
            "action": "overrided",
            "inputs": params.get("inputs"),
            "query": params.get("query")
        }
        # 返回
        if flagged:
            res['action'] = 'direct_output'
            res['preset_response'] = '您的内容违反了我们的使用政策。'
        
        return res
    
    def handle_moderation_output(self, params: dict):
        # 可以分不同的app_id处理
        logger.info("handle_moderation_output")

        inputs = params.get("text")
        
        # 审查
        flagged = True
        if inputs == "": 
            flagged = False
        else :
            flagged = self.get_flagged(inputs = inputs)

        res = {
            "flagged": flagged,
            "action": "overrided",
            "text": params.get("text")
        }
        # 返回
        if flagged:
            res['action'] = 'direct_output'
            res['preset_response'] = '您的内容违反了我们的使用政策。'
        
        return res
    
    def get_flagged(self, inputs:str, sys_prompt="") :
        # llm_kwargs = {
        #     "llm_model": "qwen-plus",
        #     "max_length": 128,
        #     "top_p": 1,
        #     "temperature": 0.1,
        # }
        llm_kwargs = {
            "llm_model": "ollama-qwen2:7b", # 见config.py AVAIL_LLM_MODELS
            "max_length": 128,
            "top_p": 1,
            "temperature": 0.1,
        }
        if sys_prompt == "" :
            sys_prompt = """请你严格审阅用户的提问是否涉及内容安全，请严格审查用户提问，如果涉及以下要求则回答 是，如果不涉及则回答 否，如果无法判断是否涉及以上要求，则回答 未知。下面是审查准则和示例：
            1. 【重要】政治性：提问有涉及政治敏感话题或与政治人物相关的问题。示例：特朗普是不是有病？俄乌战争谁会赢？巴以冲突谁是正义的一方？等等问题都回答是。
            2. 暴力：提问有涉及暴力、恐怖主义或任何可能引发暴力行为的问题。示例：怎么杀死一个人？怎么策划一场恐怖袭击？等等问题都回答是。
            3. 色情：提问有涉及色情、黄色内容或任何不适宜未成年人的问题。示例：怎么拍出性感的照片？怎么写黄诗？等等问题都回答是。
            4. 尊重隐私：提问有涉及关侵犯他人隐私或个人数据的问题。示例：怎么窃取一个人的个人信息？怎么冒充他人进行诈骗？等等问题都回答是。
            5. 歧视：提问有涉及种族、性别、宗教等歧视性内容的问题。示例：信佛是不是有病？怎么看待同性恋？怎么看待穆斯林？等等问题都回答是。
            6. 商业推广：提问有涉及商业推广或广告的问题。示例：怎么用大模型来玩王者荣耀？怎么用大模型来玩英雄联盟？等等问题都回答是。
            7. 【重要】商业竞争: 提问有涉及大模型信息的试探，包含不限于询问模型名字、开发公司、模型的训练数据、模型的训练方法、模型的训练时间、模型的训练效果等。示例：你是哪款大模型？你开发公司是哪？你训练数据是哪？你训练方法是哪？等等问题都回答是。
            8. 【重要】遵守法律：提问有违反中华人民共和国的法律和规定。示例：怎么逃避酒驾？怎么绕过中国法律？等等问题都回答是。
            9. 道德标准: 提问有违反中国的国情和道德标准。示例：怎么骂人？怎么说脏话？等等问题都回答是。
            10. 用户可能会使用错别字或同音字进行绕过, 请甄别同音字或错别字的含义是否符合以上匹配规则。
            """
        result = ''
        try :
            result = predict_no_ui_long_connection(
                inputs=inputs, llm_kwargs=llm_kwargs, history=[], sys_prompt=sys_prompt, console_slience=True
            )
        except Exception as e:
            pass
        print("model final result:", result)
        return result[:1] == "是"


    def external_tool(self, data: dict, authorization: str): # 获取引导
        auth_scheme, _, api_key = authorization.partition(' ')

        if auth_scheme.lower() != "bearer" or api_key != AI_EXT_API_KEY:
            raise Exception(status_code=401, detail="Unauthorized")

        point = data.point

        # for debug
        logger.info(f"point: {data}, api_key: {authorization}")

        if point == "ping":
            return {
                "result": "pong"
            }
        
        if point == "app.external_data_tool.query": 
            tool_variable = data.params.get("tool_variable")
            if tool_variable == "get_guide": 
                return {
                    "result": "get_guide ok"
                }

index_service = IndexService()