from fastapi import APIRouter, Body, Header, Query
from .index_service import index_service 
from .amap_service import amap_service 
from .index_request import ContentReviewInput
from toolbox import get_conf
AI_ENV = get_conf('AI_ENV')


router = APIRouter()

@router.get('/test')
def test():  
    try :
        return index_service.test("xxxx")
    except Exception as e:
        if AI_ENV != 'prod':
            raise e
        return {'code':500, 'data':'', 'msg':'error: %s' % e}

@router.post('/content_review')
def content_review(data: ContentReviewInput = Body(...), authorization: str = Header(None)):
    """
    内容审查
    """
    try :
        return index_service.content_review(data, authorization)
    except Exception as e:
        if AI_ENV != 'prod':
            raise e
        return {'code':500, 'data':'', 'msg':'error: %s' % e}

@router.post('/external_tool')
def external_tool(data: ContentReviewInput = Body(...), authorization: str = Header(None)):
    """
    获取指南
    """
    try :
        return index_service.external_tool(data, authorization)
    except Exception as e:
        if AI_ENV != 'prod':
            raise e
        return {'code':500, 'data':'', 'msg':'error: %s' % e}

@router.get('/get_amap_lng_lat')
def get_amap_lng_lat(address: str = Query(None, description='地址'), 
                     auth_var: str = Query(None, description='高德开放平台登录后cookie中value')):
    try :
        return amap_service.get_amap_lng_lat(address, auth_var)
    except Exception as e:
        if AI_ENV != 'prod':
            raise e
        return {'code':500, 'data':'', 'msg':'error: %s' % e}
