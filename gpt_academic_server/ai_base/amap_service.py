import os
from playwright.sync_api import sync_playwright
import time
from datetime import datetime, timedelta

class AmapService:

    def get_amap_lng_lat(self, address:str, auth_val:str):
        # 判断address是否为空
        if address == '':
            return {'location':','}

        user_agent = os.getenv('USER_AGENT', "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(headless=False)  # headless=False 表示显示浏览器窗口
            context = browser.new_context(user_agent=user_agent)
            # 判断auth_val是否为空
            if auth_val != '':
                cookies = [
                            {
                                "name": "passport_login",
                                "value": auth_val,
                                "domain": ".amap.com",
                                "path": "/",
                                "expires": (datetime.now() + timedelta(days=1)).timestamp(),
                                "httpOnly": True,
                                "secure": True,
                                "sameSite": "None",
                            }
                        ]
                context.add_cookies(cookies)

            page = context.new_page()

            # 打开高德地图经纬度选择器
            page.goto('https://lbs.amap.com/tools/picker')

            # 等待页面加载完成
            page.wait_for_selector("input[id='txtSearch']")

            # 输入地址并搜索
            page.fill("input[id='txtSearch']", address)
            page.click('.btn-search')

            # 提取经纬度信息
            time.sleep(1)
            lnglat = page.input_value('#txtCoordinate')
            # 判断如果为空
            if lnglat == '':
                print('休息5秒')
                time.sleep(5) 
                lnglat = page.input_value('#txtCoordinate')
            
            # 关闭浏览器
            browser.close()
            return {'location':lnglat}


amap_service = AmapService()