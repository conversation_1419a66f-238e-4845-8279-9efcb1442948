from typing import Any, Union

import requests
import json
import logging

from core.tools.entities.tool_entities import ToolInvokeMessage
from core.tools.tool.builtin_tool import BuiltinTool


class GuideQuery(BuiltinTool):
    def _invoke(self,
                user_id: str,
                tool_parameters: dict[str, Any],
                ) -> Union[ToolInvokeMessage, list[ToolInvokeMessage]]:
        """
            invoke tools
        """
        content = tool_parameters.get('content', '').split(',')[0]
        res = {}
        if not content:
            res["status"] = 500
            res["message"] = 'Invalid parameter content'
            return self.create_text_message(json.dumps(res))

        try:
            res = self._query(user_id, tool_parameters)
        except Exception:
            res["status"] = 500
            res["message"] = 'Query service error, please check the network'

        return self.create_text_message(json.dumps(res))

    def _query(self, user_id: str,
                tool_parameters: dict[str, Any]) -> dict:
        try:
            # url = "https://out.s.medsci.cn/ojlgulcc/ojlgulcc_search"
            url = "https://out-s.medon.com.cn/ojlgulcc/ojlgulcc_search"
            params = {
                "module": 'guider',
                "content": tool_parameters.get('content', '').split(',')[0],
                "pageIndex": 1,
                "pageSize": tool_parameters.get('pageSize', '10'),
                "user_id": user_id,
                "user_info": ""
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36"
            }

            response = requests.post(url, params=params, headers=headers)
            if response.status_code != 200:
                return {
                    "status": 500,
                    "message": str("Query service return: " + response.text)
                }

            response_json = response.json()
            logging.info(json.dumps(response_json))
            json_res = response_json.get('result', {}).get('data', {})

            data = json_res.get('data')
            # 指定你想要保留的字段
            fields_to_keep = ["id", "title", "enTitle", "guiderFrom", "summary", "publishedTime"]
            # 使用列表推导式和字典推导式来筛选字段
            filtered_data = [{k: d[k] for k in fields_to_keep if k in d} for d in data]
            json_res['data'] = filtered_data

            return json_res
        except Exception as e:
            return {
                "status": 500,
                "message": str(e)
            }
