from typing import Any

from core.tools.errors import ToolProviderCredentialValidationError
from core.tools.provider.builtin.medsci_guide.tools.guide import GuideQuery
from core.tools.provider.builtin_tool_provider import BuiltinToolProviderController


class JsonExtractProvider(BuiltinToolProviderController):
    def _validate_credentials(self, credentials: dict[str, Any]) -> None:
        try:
            GuideQuery().invoke(user_id='',
                                     tool_parameters={
                                         "content": "这是一段测试文本",
                                         "dest": "en"
                                     })
        except Exception as e:
            raise ToolProviderCredentialValidationError(str(e))
