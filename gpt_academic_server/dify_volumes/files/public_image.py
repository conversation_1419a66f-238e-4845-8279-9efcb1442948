from flask import Response, request
from flask_restful import Resource
from werkzeug.exceptions import NotFound

import services
from controllers.files import api
from libs.exception import BaseHTTPException
from services.account_service import TenantService
from services.file_service import FileService


class PublicImageApi(Resource):
    def get(self, file_id):
        file_id = str(file_id)

        if not file_id:
            raise NotFound('image is not found')

        try:
            generator, mimetype = FileService.get_public_image_preview(
                file_id,
            )
        except services.errors.file.UnsupportedFileTypeError:
            raise UnsupportedFileTypeError()

        return Response(generator, mimetype=mimetype)

api.add_resource(PublicImageApi, '/files/public/<uuid:file_id>/image-preview')

class UnsupportedFileTypeError(BaseHTTPException):
    error_code = 'unsupported_file_type'
    description = "File type not allowed."
    code = 415
