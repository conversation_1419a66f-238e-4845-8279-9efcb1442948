{"name": "write-js", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --open", "build:test": "vite build --mode test", "build:prod": "vite build --mode production"}, "dependencies": {"@apollo/client": "^3.11.5", "@kangc/v-md-editor": "^2.3.18", "@umoteam/editor": "2.3", "@vue/apollo-composable": "^4.2.1", "@vueuse/head": "^2.0.0", "axios": "^1.7.5", "element-plus": "^2.8.1", "graphql": "^16.9.0", "graphql-tag": "^2.12.6", "js-cookie": "^3.0.5", "marked": "^14.1.2", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "vant": "^4.9.19", "vue": "^3.4.29", "vue-i18n": "^10.0.4", "vue-qr": "^4.0.9", "vue-router": "^4.3.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "vite": "^5.3.1"}}