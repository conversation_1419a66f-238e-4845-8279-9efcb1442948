// vite.config.js
import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "file:///D:/%E9%A1%B9%E7%9B%AE/main/yudao-ui-ai-write/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/%E9%A1%B9%E7%9B%AE/main/yudao-ui-ai-write/node_modules/@vitejs/plugin-vue/dist/index.mjs";
var __vite_injected_original_import_meta_url = "file:///D:/%E9%A1%B9%E7%9B%AE/main/yudao-ui-ai-write/vite.config.js";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    plugins: [
      vue()
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      }
    },
    base: "/write/",
    build: {
      outDir: "../ai-write-public/write",
      // 将输出目录设置为 'build'
      assetsDir: "static",
      // 静态资源目录设置为 'static'
      sourcemap: false
      // 生产环境不生成 source map
    },
    server: {
      port: 3001,
      //启动端口
      open: false,
      // 反向代理
      proxy: {
        [env.VITE_DIFY_CONTEXT]: {
          target: env.VITE_DIFY_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_DIFY_CONTEXT}`), "")
        },
        [env.VITE_YUDAO_CONTEXT]: {
          target: env.VITE_YUDAO_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_YUDAO_CONTEXT}`), "")
        },
        [env.VITE_CHAT_CONTEXT]: {
          target: env.VITE_CHAT_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_CHAT_CONTEXT}`), "")
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
