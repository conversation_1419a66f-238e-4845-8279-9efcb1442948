
import axios from "axios"
import { ElMessage } from 'element-plus'
import { getToken } from "@/utils/auth"
import cookie from "js-cookie";
import {Cookies} from "@/utils/cookieHandler"
let baseURL = import.meta.env.VITE_YUDAO_API_URL
if (import.meta.env.MODE === 'development') {
    baseURL = ''
}
const service = axios.create({
  baseURL,
  timeout: 20000 // request timeout
})
// 发起请求之前的拦截器
service.interceptors.request.use(
  config => {
    const { url } = config;

    // 添加 locale 参数到 URL
    const locale = cookie.get('ai_apps_lang') || localStorage.getItem('ai_apps_lang') || 'zh';
    config.url = url.includes("?") ? `${url}&locale=${locale}` : `${url}?locale=${locale}`;

    if (!url.includes('/index/getAiWriteToken')) {
      const token = Cookies.get("yudaoToken")
      if (token) {
         config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`
        };
      }
      const userInfo =  JSON.parse(cookie.get("userInfo") || "{}");
       config.data = {
        ...config.data,
        user: userInfo.userName,
      };
    }
    return config
  },
  error => Promise.reject(error)
)
// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    if (response.status !== 200) {
      return Promise.reject(new Error(res.msg || "Error"))
    } else {
      if (res.code === 0) {
        return res
      } else {
        if (res && Array.isArray(res)) {
          return res
        } else {
          ElMessage.error(res.msg || "Error")
          return {}
        }
      }
    }
  },
  error => {
    return Promise.reject(error)
  }
)
export default service
