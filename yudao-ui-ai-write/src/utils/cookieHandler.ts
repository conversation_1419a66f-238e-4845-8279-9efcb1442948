import Cookie from 'js-cookie';

const domainMap: Record<string, string> = {
  '.medsci.cn': '.medsci.cn',
  '.medon.com.cn': '.medon.com.cn',
  'localhost': 'localhost'
};

interface CookieOptions {
  expires?: number;
  domain?: string;
  path?: string;
  secure?: boolean;
  [key: string]: any;
}

export const Cookies = {
  /**
   * 设置带有 domain 和 expires 的 Cookie
   * @param name Cookie 名称
   * @param value Cookie 值（自动 JSON 序列化）
   * @param options 额外配置项
   */
  set: (name: string, value: any, options: CookieOptions = {}): void => {
    const { expires = 365, domain, ...restOptions } = options;
    let finalDomain = domain;
    if (!finalDomain) {
      const matchedDomain = Object.keys(domainMap).find(d => location.origin.includes(d));
      if (matchedDomain) {
        finalDomain = domainMap[matchedDomain];
      }
    }
    const finalOptions: CookieOptions = {
      expires,
      ...(finalDomain ? { domain: finalDomain } : {}),
      ...restOptions
    };
    Cookie.set(name, value, finalOptions);
  },

  /**
   * 获取 Cookie 并自动反序列化
   * @param name Cookie 名称
   * @returns Cookie 值
   */
  get: (name: string): any => {
    const value = Cookie.get(name);
    try {
      return value ? value : null;
    } catch {
      return value;
    }
  },
  /**
   * 删除 Cookie
   * @param name Cookie 名称
   * @param options 额外配置项（如 domain、path）
   */
  remove: (name: string, options: CookieOptions = {}): void => {
    let finalDomain = options.domain;
    if (!finalDomain) {
      const matchedDomain = Object.keys(domainMap).find(d => location.origin.includes(d));
      if (matchedDomain) {
        finalDomain = domainMap[matchedDomain];
      }
    }
    const finalOptions: CookieOptions = {
      ...(finalDomain ? { domain: finalDomain } : {}),
      ...(options.path ? { path: options.path } : {})
    };
    Cookie.remove(name, finalOptions);
}
}

  