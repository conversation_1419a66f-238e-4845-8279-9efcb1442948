export const injectCustomCode = (customCss,customJs) => {
    const style = document.createElement('style');
      style.type = 'text/css';
      style.innerHTML = customCss;
      document.head.appendChild(style);
      // 创建一个新的script元素
      var script = document.createElement('script');
      script.type = 'text/javascript';
      // 设置脚本内容
      script.text = customJs;
      // 将script元素添加到body的末尾
      document.body.appendChild(script);
  }