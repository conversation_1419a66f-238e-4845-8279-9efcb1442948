
import { createI18n } from 'vue-i18n';
import { getDefaultLanguageCode,transformArrayToObject,setItemWithTimestamp,getItemWithTimestampCheck } from "@/common/commonJs";
import { getConfigPage } from "@/api/dify";
// import zh_CN from '@/langs/zh-CN';
// import en from '@/langs/en-US';
// import pt from '@/langs/pt-BR';
// import ar from '@/langs/ar-SA';
// import es from '@/langs/es-ES';
// import id from '@/langs/id-ID';
// import ja from '@/langs/ja-JP';
// import ko from '@/langs/ko-KR';
// import ms from '@/langs/ms-MY';
// import vi from '@/langs/vi-VN';
// import zh_TW from '@/langs/zh-TW';
async function getMessages() {
  let langsStr = getItemWithTimestampCheck('current_langs_pack', 7)
  let umoData = getItemWithTimestampCheck('current_langs_pack_umo', 7)
  if (!langsStr||!umoData) {
    try {
      const res = await ajax.get(getConfigPage)
      if (res.list.length !== 0) {
        langsStr = JSON.stringify(transformArrayToObject(res.list))
        setItemWithTimestamp('current_langs_pack', langsStr);
        let filteredData = res.list.filter(e => e.value != "{}" && e.key.includes(".dify"));
        let umoData = filteredData.reduce((acc, item) => {
          // 如果对象中还没有这个键，初始化一个空数组
          if (!acc[item.key.substr(0, item.key.indexOf('.'))]) {
            acc[item.key.substr(0, item.key.indexOf('.'))] = {};
          }
          // 将当前项推入对应键的数组
          acc[item.key.substr(0, item.key.indexOf('.'))] = (JSON.parse(item.value));
          return acc;
        }, {});
        
        setItemWithTimestamp('current_langs_pack_umo', JSON.stringify(umoData));
      }
      
    } catch (error) {
      console.error('Failed to load language pack:', error);
    }
  }

  return JSON.parse(langsStr)
}

let defaultLanguage = getDefaultLanguageCode()

export default async function createI18nInstance (locale = defaultLanguage) {
  const messages = await getMessages()
  return createI18n({
    legacy: false, // 使用 Composition API 模式
    globalInjection: true,
    locale: locale,
    fallbackLocale: defaultLanguage, // 如果没有找到匹配的语言，默认浏览器默认语言
    messages,
  });
}
