<template>
  <div class="assistant-container" :class="{ 'is-collapsed': isCollapsed }">
    <div
      class="assistant-icon"
      @click="toggleCollapse"
    >
      <!-- 这里可以放置小助理的图标，例如使用font-awesome -->
      <img
        src="@/assets/images/kefu.png"
        class="fas fa-user-astronaut"
        alt="客服"
      />
    </div>
    <div class="qr-code" :class="{ 'is-visible': !isCollapsed && isQrCodeVisible }">
      <!-- 这里放置二维码图片 -->
      <img src="@/assets/images/qrcode.png" alt="QR Code" />
      扫码添加学术老师
    </div>
  </div>
</template>

<script>
export default {
  name: "AssistantComponent",
  data() {
    return {
      isCollapsed: false,
      isQrCodeVisible: false,
      isMobile: false
    };
  },
  mounted() {
    this.checkMobile();
    window.addEventListener('resize', this.checkMobile);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.checkMobile);
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
      if (!this.isCollapsed) {
        this.isQrCodeVisible = true;
      } else {
        // 延迟隐藏二维码，等待过渡动画完成
        setTimeout(() => {
          this.isQrCodeVisible = false;
        }, 300);
      }
    },
    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
      if (this.isMobile) {
        this.isCollapsed = true;
        this.isQrCodeVisible = false;
      }
    }
  }
}
</script>

<style scoped>
.assistant-container {
  position: fixed;
  right: 20px;
  bottom: 100px;
  z-index: 100;
  transition: all 0.3s ease;
}
/* 在非移动端保留悬停显示功能 */
@media (min-width: 769px) {
  .assistant-icon:hover ~ .qr-code:not(.is-visible) {
    display: block; 
  }
  .assistant-icon:active ~ .qr-code:not(.is-visible) {
    display: block; 
  }
}
.assistant-icon {
  cursor: pointer;
  width: 62px;
  transition: all 0.3s ease;
  z-index: 101;
  img{
    width: 100%;
  }
}

/* 移动端样式 */
@media (max-width: 768px) {
  .assistant-container.is-collapsed .assistant-icon {
    transform: translateX(40px);
    opacity: 0.6;
  }
  
  .assistant-container:not(.is-collapsed) .assistant-icon {
    transform: translateX(0);
    opacity: 1;
  }
}
.qr-code::before {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  position: absolute;
  right: -5px;
  bottom: 25px;
  background-color: #fff;
  z-index: 1000;
  transform: rotate(45deg);
}
.qr-code {
  display: none;
  position: fixed;
  bottom: 100px;
  width: 200px;
  z-index: 1000;
  right: 92px;
  background: #fff;
  border-radius: 4px;
  text-align: center;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  padding: 10px 14px;
  transition: all 0.3s ease;
}

.qr-code.is-visible {
  display: block;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .qr-code {
    right: 82px;
  }
}

.qr-code img {
  width: 100%;
  height: auto; /* Adjusted to maintain aspect ratio */
}
</style>
