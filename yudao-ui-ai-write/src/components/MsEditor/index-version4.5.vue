<template>
    <umo-editor v-bind="options" ref="editorRef" @changed:activeCategory="onChangedActiveCategory"
        @changed:selection="onChangedSelection" @changed:getMessage="onChangedAi" @changed:retry="onChangedRetry"
        @changed:stopMessage="onChangedStopMessage" @changed:FeedbackDislike="onChangedFeedbackDislike"
        @changed:FeedbackLike="onChangedFeedbackLike" @changed:newConversation="onChangedNewConversation"
        @changed:showLog="onChangedShowLog" @changed:collect="onChangedCollect"
        @changed:leadQuestionOrRecord="onChangedLeadQuestionOrRecord"
        @changed:dislikeReason="onChangedDislikeReason" @save="onSave">
    </umo-editor>
    <MsMessages v-model="dialogVisible" :classInfo="classInfo" />
</template>
<script setup>
import { useRoute } from 'vue-router';
import { UmoEditor } from '@umoteam/editor';
import { ref, watch, reactive, onMounted, nextTick } from 'vue'
import { useCategory } from '@/hooks/editor/useCategory' //获取目录
import { useType } from '@/hooks/editor/useType' // 获取简易操作目录
import { useLeadingQuestion } from '@/hooks/editor/useLeadingQuestion'  //引导提问
import { useFeedback } from '@/hooks/editor/useFeedback'  //踩/点赞
import { useConversations } from '@/hooks/editor/useConversations' //历史会话
import { useAnswer } from '@/hooks/editor/useAnswer' //获取答案
import { useStopMessage } from '@/hooks/editor/useStopMessage' //停止会话
import { useCollectionList } from '@/hooks/editor/useCollectionList' //获取会话收藏记录
import { useCollection } from '@/hooks/editor/useCollection' //收藏/取消收藏
import { useDialikeReason } from '@/hooks/editor/useDialikeReason' //提交吐槽原因
import { usePrompt } from '@/hooks/editor/usePrompt' //获取当前模板实例的语料
import { useTemplatePrompt } from '@/hooks/editor/useTemplatePrompt' //获取当前模板实例的语料
import { removeSession, getSession, setSession } from "@/utils/auth"
import { useEditorStore } from '@/stores/editor'  
import { useLoginStore } from '@/stores/login'
const loginStore = useLoginStore()
const route = useRoute();
const editorStore = useEditorStore()
const editorRef = ref(null);
let dialogVisible = ref(false)
let spsPrompt = ref('')
const classInfo = reactive({
    conversation_id: '',
    message_id: '',
    app_id: '',
    underlineText: '',
    task_id: ''
})
const options = reactive({
    theme: 'light',
    height: 'calc(100vh - 50px)',
    page: {
        ai: {
            list: [],
            operateTag: [
                { label: '替换', value: 'replace', theme: '' },
                { label: '追加', value: 'add', theme: '' },
                { label: '放弃', value: 'cancel', theme: 'danger' },
                { label: '重试', value: 'again', theme: '' },
            ],
            operateSvg: [
                { label: '全部对话', value: 'log', svg: 'log', svgActive: 'log', isActive: false },
                { label: '复制', value: 'copy', svg: 'double', svgActive: 'double', isActive: false },
                { label: '收藏', value: 'collect', svg: 'star', svgActive: 'starActive', isActive: false },
                { label: '有用', value: 'like', svg: 'thump-up', svgActive: 'up', isActive: false },
                { label: '吐槽', value: 'dislike', svg: 'thump-down', svgActive: 'down', isActive: false },
            ],
            fixedOperate: [
                { tip: '提问引导', svg: 'compass', label: '提问引导', value: 'guide', data: [], title: '可以这样提问：' },
                // { tip: '历史提问', svg: 'log', label: '历史提问', value: 'history', data: [] , title: '历史提问'},
                { tip: '执行提问', svg: 'start', label: '执行提问', value: 'query' },
                { tip: '隐藏AI', svg: 'close1', label: '隐藏AI', value: 'close' },
            ],
            leadingQuestionList: [], //引导问题
            recordList: [] //历史记录
        },
        aiAnswer: '',
        isAnswerFinished: false
    }
});
// 获取目录
const { defaultCategotry, refetcCategory } = useCategory()

// 获取历史会话
const { conversationList, fetchConversations } = useConversations()

// 获取引导问题
const { leadingQuestions, fetchLeadingQuestions } = useLeadingQuestion()
// 点赞/踩
const { fetchFeedback } = useFeedback()
// ai会话
const { getAnswer } = useAnswer(route.params.app_uuid)
// 停止会话
const { stopMessage } = useStopMessage()
//获取会话收藏记录
const { refetchCollection, collectionList } = useCollectionList()
//收藏/取消收藏
const { insertCollection, updateCollection } = useCollection()
// 提交吐槽原因
const { dislikeReasonRecord, insertDislikeReason, updateDislikeReason, getDislikeReasonRecord } = useDialikeReason()

// const { fetchPrompt, prompt } = usePrompt()
const { fetchTemplatePrompt, prompts } = useTemplatePrompt()


onMounted(() => {
    fetchTemplatePrompt()
})
// watch(
//     () => loginStore.isLoggedIn,
//     newVal => {
//         if (newVal) {
//             fetchLeadingQuestions()
//             fetchConversations('正常')
//             fetchPrompt()
//         }
//     },
//     { immediate: true }
// )
// watch(
//     () => prompt.value,
//     newVal => {
//         spsPrompt.value = newVal
//     }
// )
const num = ref(0)
// // 回填默认的大纲
watch(
    () => defaultCategotry.value,
    (newVal) => {
        editorRef?.value?.setContent(newVal || '')
    }
)

//引导提问
watch(
    () => leadingQuestions.value,
    (newVal) => {
        const parent = options.page.ai.fixedOperate.find(item => item.value === 'guide')
        if (newVal) {
            parent.data = newVal.slice(0, 20).map(item => {
                return {
                    query: item,
                    label: item,
                }
            })
        }
    }
)
// 历史会话
watch(
    () => conversationList.value,
    (newVal) => {
        const parent = options.page.ai.fixedOperate.find(item => item.value === 'history')
        if (parent) {
            parent.data = newVal.slice(0, 20).map(item => {
                return {
                    query: item.name
                }
            })
        }
    }
)


// 获取简易操作分类
const { typeList, refetchType } = useType()
watch(
    () => typeList.value,
    (newVal) => {
        options.page.ai.list = newVal
    }
)

// 获取点击的大纲信息
const onChangedActiveCategory = ({ activeCategory }) => {
    console.log(activeCategory)
}

// 划词内容
const onChangedSelection = () => {
    // classInfo.underlineText = editor.commands.getSelectionText()
    // classInfo.underlineText =  getSelectionText(editor)
}

let queryLast = reactive({})

// 重试
const onChangedRetry = async () => {
    classInfo.underlineText = ''
    await getAnswer(options, queryLast, classInfo, spsPrompt)
};

// 获取答案
const onChangedAi = async (aiDemand) => {
    if (Object.keys(aiDemand).length) {
        queryLast = { ...aiDemand }
        await getAnswer(options, aiDemand, classInfo, spsPrompt)
    }
};

// 停止会话
const onChangedStopMessage = async () => {
    stopMessage(options, classInfo)
}

// 新对话
const onChangedNewConversation = async (aiDemand) => {
    handlePrompt()
    options.page.ai.operateSvg = options.page.ai.operateSvg.map(item => ({
        ...item,
        isActive: false
    }));
    classInfo.conversation_id = ''
    if (Object.keys(aiDemand).length) {
        queryLast = { ...aiDemand }
        classInfo.app_id = aiDemand.dify_app_uuid || '101ffbe6-1d12-42ab-aee1-ed8c63287fe9'
        await getAnswer(options, aiDemand, classInfo, spsPrompt)
    }
}

// 获取预料
const handlePrompt = async () => {
    classInfo.underlineText = editorRef.value.getSelectionText()
    // classInfo.selectionNode = editorRef.value.getSelectionNode()
    // const arr = editorRef.value.getTableOfContents()
    // let basePath = `写作模板/${route.params.name}`
    // let str = basePath;
    // arr.forEach(item => {
    //     if (item.id === classInfo.selectionNode.attrs.id) {
    //         str += `/${item.textContent}`
    //     }
    // })
    // spsPrompt.value = findPrompt(str);
    // if (!spsPrompt.value) {
        // spsPrompt.value = findPrompt(basePath) || '';
    // }
}

// 查找匹配的提示
const findPrompt = (path) => {
    if (!prompts.value) return;
    return prompts.value.find(item => item.name === path)?.prePrompt;
};

// 吐槽 或 点赞 接口
const onChangedFeedback = async (type) => {
    const response = await fetchFeedback({
        ...classInfo,
        rating:type
    });
    if (response.result === 'success') {
        setOperateSvg(type);
    }
}

// 吐槽
const onChangedFeedbackDislike = async () => {
    const obj = options.page.ai.operateSvg.find(item => item.value === 'dislike')
    const type = obj.isActive ? null : 'dislike'
    onChangedFeedback(type);
}
// 点赞
const onChangedFeedbackLike = async () => {
    const obj = options.page.ai.operateSvg.find(item => item.value === 'like')
    const type = obj.isActive ? null : 'like'
    onChangedFeedback(type);
}

// 修改operateSvg数据中“吐槽”或“喜欢”的active状态
const setOperateSvg = (rating) => {
    options.page.ai.operateSvg.forEach((item) => {
        if (['like', 'dislike' ].includes(rating)) {
            if (item.value === 'like' || item.value === 'dislike') {
                item.isActive = (item.value === rating);
            }
        } else if (['正常', '删除'].includes(rating)) {
            if (item.value === 'collect') {
                item.isActive = rating === '正常' ? true : false
            }
        }
    });
}

// 从答案面板打开历史弹窗
const onChangedShowLog = () => {
    dialogVisible.value = true
}

// 收藏
const onChangedCollect = async () => {
    await refetchCollection({ conversation_id: classInfo.conversation_id })
    if (Array.isArray(collectionList.value) && collectionList.value.length) {
        const obj = collectionList.value[0]
        await updateCollection({
            conversation_id: obj.conversation_id,
            from_end_user: obj.from_end_user,
            status: obj.status === '正常' ? '删除' : '正常'
        })
        setOperateSvg(obj.status === '正常' ? '删除' : '正常')
    } else {
        await insertCollection({
            conversation_id: classInfo.conversation_id,
            from_end_user: loginStore.userInfo.userName
        })
        setOperateSvg('正常')
    }

}

// 获取引导问题 或 历史记录
const onChangedLeadQuestionOrRecord = (params) => {
    if (params === 'guide') {
        fetchLeadingQuestions()
    } else {
        fetchConversations('正常')
    }
}
// 提交不喜欢原因
const onChangedDislikeReason = async (params) => {
    await getDislikeReasonRecord({
        message_id: classInfo.message_id,
    })
    const { ext, reason } = params
    const obj = {
        message_id: classInfo.message_id,
        ext: ext.join('&'),
        reason: reason
    }
    if (dislikeReasonRecord.value.length) {
        updateDislikeReason(obj)
    } else {
        insertDislikeReason(obj)
    }
}

// 保存
const onSave =  async (content, page, document) => {
  
}

</script>