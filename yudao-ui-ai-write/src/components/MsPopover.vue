<template>
    <el-button v-if="type === 'button'" ref="buttonRef" v-click-outside="onClickOutside" class="btn" type="primary" round plain>
        <slot name="btn" />
        <img :src="right" alt="" style="width:10px; height: 10px;margin-left: 6px;">
    </el-button>
    <img  v-if="type === 'img'" ref="buttonRef" v-click-outside="onClickOutside" :src="src" alt="" class="imgSty">

    <el-popover ref="popoverRef" :virtual-ref="buttonRef" trigger="click" virtual-triggering width="450"
        popper-class="msPopover" placement="bottom-end" @show="handleShow">
        <img @click="hidePopover" :src="close" alt="" class="close">
        <slot name="title" />
        <el-scrollbar height="330px" class="scrollbarBox msScrollbar" always>
            <slot name="content" />
        </el-scrollbar>
    </el-popover>
</template>
<script setup lang="ts">
import { ref, unref } from 'vue'
import { ClickOutside as vClickOutside } from 'element-plus'
import right from '@/assets/images/right.png'
import close from '@/assets/images/close.png'

defineProps({
    type: {
        type: String,
        default: 'button'
    },
    src: {
        type: String,
        default: ''
    }
})
const buttonRef = ref()
const popoverRef = ref()
const onClickOutside = () => {
    unref(popoverRef).popperRef?.delayHide?.()
}

const emits = defineEmits(['open'])
const handleShow = () => {
    emits('open')
}

const hidePopover = () => {
    popoverRef.value?.hide()
}
defineExpose({ hidePopover })
</script>
<style scoped>
.scrollbarBox .el-row {
    margin: 10px 0px; 
    border-radius: 10px;
    border: 1px solid #dcdfe6;
    color: var(---el-text-color-regular);
    font-size: var(--el-font-size-extra-small);
    padding: 8px;
    cursor: pointer;
}
.btn {
    margin-right: 10px;
    font-weight: 400;
    font-size: 12px;
    padding: 1px 10px;
    height: 25px;
}

.close{
    width:12px; 
    height: 12px;
    float: right;
    cursor:pointer;
}
.imgSty{
    width: 25px;
    height: 25px;
    margin-left: 6px;
    cursor: pointer;
}
</style>