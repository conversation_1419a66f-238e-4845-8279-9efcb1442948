<template>
    <el-dialog v-model="localVisible" width="900" :title="`${$t('tool.关于')}“${title}”${$t('tool.的全部对话')}`" class="record-dialog msDialog" top="10vh"
        @close="handleClose">
        <el-scrollbar height="500px" always class="msScrollbar">
            <div v-for="item in list" :key="item.date" class="log-item">
                <el-row class="info">
                    <img src="@/assets/images/default.png" alt="" class="defaultImg">
                    <span class="query">{{ item.query }}</span>
                </el-row>
                <div class="info answerItem">
                    <img src="@/assets/images/robot.png" alt="" class="defaultImg" style="width: 22px;">
                    <div class="log-answer">
                        <v-md-preview :text="msMarked(item.answer)"
                            :class="['mdPreview', { expanded: item.showAll }]"></v-md-preview>
                        <el-row class="icon" style="justify-content: flex-end;align-items: center;">
                            <span @click="showMore(item)" style="color:#409eff;">{{
                                item.showAll ? `${$t('tool.收起')}` : `${$t('tool.全部内容')}` }}</span>
                            <img @click="handleOperate(item, 'copy')" :src="copy" class="msSvg" alt="">
                            <img @click="handleOperate(item, 'like')"
                                :src="item.message_feedback?.rating === 'like' ? upChoose : up" class="msSvg" alt="">

                            <el-popover :visible="item.visible" placement="top-end" :width="260"
                                style="border-radius: 20px;">
                                <p>
                                    <span>您的反馈帮助AI助手成长进步</span>
                                    <el-icon @click="item.visible=false" style=" float: right;cursor: pointer;">
                                        <Close color="#909399" />
                                    </el-icon>
                                </p>
                                <el-checkbox-group v-model="dislikeForm.checkList" size="small">
                                    <el-checkbox v-for="item in dislike_reason_list" :label="item" :value="item" />
                                </el-checkbox-group>
                                <el-input v-model="dislikeForm.reason" style="width: 220px" maxlength="200" size="small"
                                    class="dislikeInput" placeholder="其他原因" :rows="2" show-word-limit type="textarea" />
                                <div style="text-align: center; margin: 0">
                                    <el-button size="small" type="primary" class="btn"
                                        @click="onChangedDislikeReason(item)" :style="{
                                        'cursor': isAllow ? 'pointer' : 'not-allowed',
                                        'opacity': isAllow ? 1 : 0.5
                                    }">
                                        提交
                                    </el-button>
                                </div>
                                <template #reference>
                                    <img @click="handleOperate(item, 'dislike')"
                                        :src="item.message_feedback?.rating === 'dislike' ? downChoose : down"
                                        class="msSvg" alt="">
                                    <el-button @click="visible = true">Delete</el-button>
                                </template>
                            </el-popover>
                        </el-row>
                    </div>
                </div>
            </div>
        </el-scrollbar>
        <!-- <div slot="footer" class="dialog-footer msFooter">
            <el-button @click="handleClose" size="small">关闭</el-button>
        </div> -->
    </el-dialog>
</template>
<script setup lang="ts">
import msMarked from '@/utils/marked';
import { Close } from '@element-plus/icons-vue'
import copy from '@/assets/images/copy.png'
import up from '@/assets/images/up.png'
import upChoose from '@/assets/images/upChoose.png'
import down from '@/assets/images/down.png'
import downChoose from '@/assets/images/downChoose.png'
import { ElMessage } from 'element-plus'
import { ref, watch, computed, reactive } from 'vue'
import { useMessages  } from '@/hooks/editor/useMessages'
import { useFeedback } from '@/hooks/editor/useFeedback'
import { useEditorStore } from '@/stores/editor'  
import { useDialikeReason } from '@/hooks/editor/useDialikeReason' //提交吐槽原因
import { useI18n } from 'vue-i18n';
const { dislikeReasonRecord, getDislikeReasonRecord, insertDislikeReason, updateDislikeReason } = useDialikeReason()
const { messagesList, refetchMessages } = useMessages()
const { fetchFeedback } = useFeedback()
const { t } = useI18n();
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true
    },
    classInfo: {
        type: Object,
        required: true
    }
});
const emit = defineEmits(['update:modelValue']);

let dislikeForm = reactive({
    checkList: [],
    ext: '',
    reason: ''
})

const dislike_reason_list = [
    '格式问题',
    '逻辑问题',
    '有害信息',
    '事实错误',
    '没有帮助',
    '答非所问'
]
const isAllow = computed(() => {
    return dislikeForm.checkList.length > 0 || dislikeForm.reason
})
const title = computed(() => {
    const query = list.value && list.value[0] ? list.value[0]?.query : '';
    if (query && query.length > 30) {
        return `${query.slice(0, 30)}...`
    }
    return query;
})


const list = ref([]);

watch(messagesList, (newMessages) => {
    list.value = newMessages.map((item) => ({
        ...item,
        showAll: false,
        isActive: false,
        visible: false
    }));
}, { immediate: true });

const localVisible = ref(props.modelValue);
watch(() => props.modelValue, (newVal) => {
    localVisible.value = newVal;
    if (newVal) {
        refetchMessages({ conversation_id: props.classInfo.conversation_id })
    } else {
        resetList();
    }
});

const resetList = () => {
    list.value = messagesList.value.map((item) => ({
        ...item,
        showAll: false,
        isActive: false,
        visible: false
    }));
};

const handleOperate = async (item: object, type: string) => {
    if (type === 'copy') {
        handlePaste(item.answer)
        return
    }
    let rating = type
    if (item.message_feedback?.rating === type) {
        rating = null
    }
    if (type === 'dislike' && rating) {
        item.visible = true
    }
    const response = await fetchFeedback({
        message_id: item.id,
        app_id: item.app_id,
        rating,
    })
    if (response.result === 'success') {
        item.message_feedback['rating'] = rating
    }
}
const handleClose = () => {
    localVisible.value = false;
    emit('update:modelValue', false);
};

const showMore = (item: object) => {
    item.showAll = !item.showAll
}

// 复制 使用插件本身封装的
const handlePaste = async (params) => {
    try {
        await navigator.clipboard.writeText(params);
        ElMessage({
            type: 'success',
            message:t("tool.copysuccess")
        })
    } catch (err) {
        ElMessage(err)
    }
}

// 提交不喜欢原因
const onChangedDislikeReason = async (params: object) => {
    if(!isAllow.value) return
    await getDislikeReasonRecord({
        message_id: params.id
    })
    const obj = {
        message_id: params.id,
        ext: dislikeForm.checkList.join('&'),
        reason: dislikeForm.reason
    }
    if (dislikeReasonRecord.value.length) {
        await updateDislikeReason(obj)
    } else {
        await insertDislikeReason(obj)
    }
    params.visible = false
    dislikeForm  = {
        checkList: [],
        ext: '',
        reason: ''
    }
}
</script>
<style>
.dislikeInput .el-textarea__inner:focus{
 box-shadow: 0 0 0 1px #dcdfe6 inset!important;
}

</style>
<style scoped>
.dislikeInput{
    margin:10px 0;
}
.btn {
    cursor: not-allowed;
    padding: 5px 25px;
    background-image: linear-gradient(76deg, #3480FA 0%, #5FEFFF 98%);
    border: none;
    color: #fff;
    border-radius: 5px;
    margin-top: 20px;
    opacity: 0.7;
}

.log-item{
    margin-top: 15px;
}
.log-item:first-child {
    margin-top: 0;
}

.query{
    flex: 1;
    line-height: 20px;
}
.log-answer {
    flex: 1;
    border-radius: 6px;
    border: 1px solid #90D3FF;
    cursor: pointer;
    background: #fff;
    margin-top: -3px;
}

.info {
    display: flex;
    align-items: flex-start;
    color: #333333;
    font-size: 12px;
}
.answerItem{
    margin-top: 10px;
}
.defaultImg{
    margin-right: 5px;
    margin-top: -3px;
}
.msSvg {
    cursor: pointer;
    width: 16px;
    margin: 5px 3px;
}

.mdPreview {
    display: block;
    overflow: hidden;
    line-height: 1.5;
    /* 设置行高 */
    max-height: calc(1.40em * 5);
    /* 例如，显示 5 行 */
    transition: max-height 0.3s ease;
}

.mdPreview.expanded {
    max-height: none;
}
</style>
<style>
.record-dialog {
    background-image: url('@/assets/images/logBack.png');
    background-size: cover;
    background-repeat: no-repeat;
}


.github-markdown-body{
    font-size: 12px !important;
    padding: 10px!important;
}
</style>