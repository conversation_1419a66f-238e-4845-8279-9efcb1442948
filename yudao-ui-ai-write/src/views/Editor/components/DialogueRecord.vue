<template>
    <MsPopover ref="popoverRef" @open="handlePopoverOpen">
        <template #btn>
            {{$t('tool.AI历史对话')}}
        </template>
        <template #title>
            <el-checkbox v-model="checkList" @change="handleChange" :label="$t('tool.我的收藏')" size="small"
                class="checkBoxStyle" />
        </template>
        <template #content>
            <el-row v-for="item in list" :key="item.id" class="msRow">
                <el-col @click="handleItem(item)">{{ item.name }}</el-col>
                <el-col class="infoDate">
                    <span>{{ item.createdAt }}</span>
                    <img @click="handleCollect(item)" :src="item.status === '正常' ? collectChoose : collect"
                        class="msSvg" alt="">
                </el-col>
            </el-row>
        </template>
    </MsPopover>
    <MsMessages v-model="showDialog" :classInfo="classInfo"/>
</template>
<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useLoginStore } from '@/stores/login'
import collectChoose from '@/assets/images/collectChoose.png'
import collect from '@/assets/images/collect.png'
// import MsPopover from '/MsPopover.vue'
import { useConversations } from '@/hooks/editor/useConversations'
import { useCollection } from '@/hooks/editor/useCollection'
const { conversationList, fetchConversations } = useConversations()
const { insertCollection, updateCollection } = useCollection()
const loginStore = useLoginStore()
const checkList = ref(false)
const showDialog = ref(false)
const popoverRef = ref(null)
const list = computed(() => conversationList.value)
const status = computed(() => checkList.value ? '正常' : '')
let classInfo = reactive({})
const handleItem = (params: object) => {
    showDialog.value = true
    classInfo = {
        conversation_id: params.conversationId,
        app_id: params.appId
    }
    popoverRef.value?.hidePopover()
}
const userName = computed(() => loginStore.userInfo.userName)
const handleCollect = async (params: object) => {
    if (params.status){
        await updateCollection({
            conversation_id: params.conversationId,
            from_end_user: userName.value,
            app_id: params.appId,
            status: params.status === '正常' ? '删除' : '正常'
        })
    } else {
        await insertCollection({
            conversation_id: params.conversationId,
            from_end_user: userName.value,
            app_id: params.appId
        })
    }
    await fetchConversations(status.value)
}

const handlePopoverOpen = () => {
    fetchConversations()
}

const handleChange = () => {
    fetchConversations(status.value)
}
</script>
<style scoped>

.scrollbarBox .el-row{
    margin: 10px 0px;
    border-radius: 10px;
    border:1px solid #dcdfe6;
    color: var(---el-text-color-regular);
    font-size: var(--el-font-size-extra-small);
    padding:  8px;
    cursor: pointer;
}

.msRow:hover{
    border: 2px solid rgba(52, 128, 249, 0.3);
    border-radius: 10px;
}
.infoDate{
    display: flex;
    align-items: center;
    color:rgba(96,98,102,0.8);
    margin-top: 3px;
}

.msSvg {
    margin-left: 8px;
    width:16px;
}

.checkBoxStyle {
    margin-top: 10px;
}
</style>