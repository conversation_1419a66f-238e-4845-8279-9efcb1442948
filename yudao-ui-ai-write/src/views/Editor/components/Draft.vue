<template>
    <MsPopover>
        <template #btn>
            最近文档
        </template>
        <template #title>
            <el-checkbox-group v-model="checkList" size="small" style="margin-top: 10px;">
                <el-checkbox label="草稿" value="draft" />
                <el-checkbox label="定稿" value="finalize" />
            </el-checkbox-group>
        </template>
        <template #content>
            <el-row v-for="item in list" :key="item.id" @click="handleItem(item)" class="msRow">
                <el-col>{{ item.query }}</el-col>
                <el-col class="infoDate">
                    <span>1小时前</span>
                    <span style="margin-left: 8px;">草稿</span>
                </el-col>
            </el-row>
        </template>
    </MsPopover>
</template>
<script setup lang="ts">
import { ref } from 'vue'
// import MsPopover from './MsPopover.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const checkList = ref(['draft', 'finalize'])
const list = [
    {
        query: '外泌体在食管鳞状细胞癌中的研究进展及热点',
        id: 11
    },
     {
         query: '干细胞治疗在阿尔兹海默病中的研究进展',
        id: 12
    },
     {
         query: '阿尔兹海默病的诊断与综合治疗进展',
        id: 13
     },
     {
         query: '神经干细胞移植治疗阿尔兹海默病的机制研究进展',
        id: 14
    }
]
const handleItem = (params: object) => {
    const routeUrl = router.resolve({ path: '/', query: { id: params.id } });  
    window.open(window.location.origin + routeUrl.href, '_blank')
}
</script>
<style scoped>
.infoDate {
    color: rgba(96, 98, 102, 0.8);
    margin-top: 3px;
    display: flex;
    align-items: center;
}
.msRow:hover {
    border: 2px solid rgba(52, 128, 249, 0.3);
    border-radius: 10px;
}
</style>