import { useQuery } from "@vue/apollo-composable"
import { getAppDetailByUuid  } from '@/api/graphql'
import { ref } from "vue"
export function useCategory() {
    let defaultTitle = ref('')
    let defaultCategotry = ref('')
    const {
        refetch: refetcCategory,
        onResult
    } = useQuery(getAppDetailByUuid)
    onResult((data ) => {
        if (data?.data && data?.data?.data && Array.isArray(data?.data?.data) && data?.data?.data.length) {
            defaultCategotry.value = convertMarkdownToHtml(data?.data?.data[0]['directory_md'])
        }
    })
    return {
        defaultTitle,
        refetcCategory,
        defaultCategotry
    }
}
export const convertMarkdownToHtml = (markdown) => {
  // 使用正则表达式匹配对应的标题级别并替换为相应的HTML标签
  return markdown
    .replace(/^###### (.*$)/gim, '<h6>\$1</h6>')
    .replace(/^##### (.*$)/gim, '<h5>\$1</h5>')
    .replace(/^#### (.*$)/gim, '<h4>\$1</h4>')
    .replace(/^### (.*$)/gim, '<h3>\$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>\$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>\$1</h1>')
    .replace(/\n{2,}/g, ''); // 替换连续的换行符为 <br> 标签
}

export const extractKeys = (data, key) => {
    let arr = []
    data.forEach(item => {
        if (item[key]) {
            arr.push(item[key]);
        }
        if (item.children && item.children.length > 0) {
            arr = arr.concat(extractKeys(item.children,key));
        }
    });
    return arr;
}

export const extractPromptNames = (data) => {
    let names = []
    data.forEach(item => {
        if (!item.path)  return
        const level = item.path.split("-").length - 1
        names.push(`<h${level}>${item.prompt_name}</h${level}>`); // 添加当前项的 prompt_name
        // 递归提取子项的 prompt_name
        if (item.children && item.children.length > 0) {
            names = names.concat(extractPromptNames(item.children));
        }
    });
    return names.join('');
}
export const buildTree = (items) => {
    // 创建一个映射来保存节点引用  
    const nodeMap = new Map();

    // 第一步：遍历数据，创建节点并在映射中保存  
    items.forEach(item => {
        if(!item.path) return
        const path = item.path;
        let currentNode = null;

        // 从根开始遍历路径的每一部分  
        let currentPath = '';
        path.split('-').forEach(part => {
            currentPath += (currentPath ? '-' : '') + part; // 构建当前路径  
            // 如果当前路径的节点不存在，则创建它  
            if (!nodeMap.has(currentPath)) {
                nodeMap.set(currentPath, {
                    path: currentPath,
                    relate_id: item.relate_id,
                    entity_id: item.entity_id,
                    id: item.id,
                    prompt_name: item.detail ? item.detail.prompt_name : '',
                    prompt_description: item.detail ? item.detail.prompt_description : '',
                    dify_app_uuid: item.detail ? item.detail.dify_app_uuid : '',
                    detail: item.detail,
                    children: [],
                    prompt: ''
                });
            }
            currentNode = nodeMap.get(currentPath); // 获取当前节点  
        });
    });
    // 第二步：从映射中找出根节点（没有父级的节点），并连接子节点  
    const roots = [];
    nodeMap.forEach((node, path) => {
        // 如果节点不是根节点（即路径包含'-'），则将其添加到其父节点的children数组中  
        if (path && path.includes('-')) {
            const parentPath = path.split('-').slice(0, -1).join('-'); // 获取父路径  
            if (nodeMap.has(parentPath)) {
                const parent = nodeMap.get(parentPath);
                parent.children.push(node); // 将当前节点添加到父节点的children数组中  
            }
        } else {
            // 路径不包含'-'，说明是根节点  
            roots.push(node);
        }
    });
    return roots; // 返回树的根节点数组  
}