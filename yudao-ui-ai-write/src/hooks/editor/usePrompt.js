// 获取单个实例语料

import ajax from "@/utils/request"
import { getAppPrompt } from '@/api/dify'
import { useRoute } from 'vue-router';
import { ref } from 'vue';
export function usePrompt() {
    const route = useRoute();
    let prompt = ref('')
    const fetchPrompt = async () => {
        try {
            const response = await ajax.post(getAppPrompt, {
                appId: route.params.uuid
            })
            prompt.value = response?.data?.prePrompt
            } catch (error) {
            console.error('单个语料:', error)
        }
    } 
    return {
        prompt,
        fetchPrompt
    }
}