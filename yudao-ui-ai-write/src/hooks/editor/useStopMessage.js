import ajax from "@/utils/request"
import { stopChat } from '@/api/dify'

export function useStopMessage() {
    const stopMessage = async (options, classInfo) => {
        try {
        options.page.isAnswerFinished = false
        const response = await ajax.post(stopChat,{
            task_id: classInfo.task_id,
            appId: classInfo.app_id,
        })
        if (response?.data?.result === 'success') {
            options.page.isAnswerFinished = false
        }
    
    } catch (error) {
        options.page.isAnswerFinished = false
    }
    }
    
    return {
        stopMessage
    }
}

