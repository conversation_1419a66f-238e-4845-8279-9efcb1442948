import { computed, ref, watch } from 'vue'
import ajax from "@/utils/request"
import { getLeadingQuestions } from '@/api/dify'
import { getToken } from "@/utils/auth"
import { useRoute } from 'vue-router'

export function useLeadingQuestion() {
    const route = useRoute();
    const leadingQuestions = ref([])
	const fetchLeadingQuestions = async () => {
        try {
            const response = await ajax.post(getLeadingQuestions,{
                inputs: {
                    authorization: `Bearer ${getToken('hasuraToken')}`,
                    // template_name: route.params.name,
                    sys: {
                        files: [],
                        user_id: ""
                    }
                },
                appId: '10638b96-6232-4cc2-b1fe-6033384175e4',
                files: [],
                response_mode: 'blocking'
            })
            if (response.data?.data?.outputs?.result) {
                leadingQuestions.value = [...response.data?.data?.outputs?.result]
            }
        } catch (error) {
            console.log(error)
        }
    }
    return {
        leadingQuestions,
        fetchLeadingQuestions
    }
}

