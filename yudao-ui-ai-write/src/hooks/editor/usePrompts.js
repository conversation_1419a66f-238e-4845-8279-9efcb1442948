// 获取多个实例语料

import ajax from "@/utils/request"
import { getAppPrompts } from '@/api/dify'
import { ref } from 'vue'

export function usePrompts () {
    const prompts = ref([])
    const fetchPrompts = async (params) => {
        try {
            const response = await ajax.post(getAppPrompts, {
                appIds: params
            })
            prompts.value = response?.data?.suggested_questions || []
            } catch (error) {
            console.error('Failed to fetch leading questions:', error)
        }
    } 
    return {
        prompts,
        fetchPrompts
    }
}