// 收藏/取消收藏
import { ElMessage } from 'element-plus'
import { useMutation, useQuery } from "@vue/apollo-composable"
import { insertDislikeReasonGql, updateDislikeReasonGql, getDislikeReasonRecordGql } from '@/api/graphql'
import { ref, watch } from "vue"
export function useDialikeReason() {
    let dislikeReasonRecord = ref([])
    const { mutate: insertDislikeReason, onDone: insertOnDone } = useMutation(insertDislikeReasonGql)
    const { refetch: getDislikeReasonRecord, onResult} = useQuery(getDislikeReasonRecordGql)
    const { mutate:updateDislikeReason, onDone: updateOnDone  } = useMutation(updateDislikeReasonGql)
    onResult((res) => {
        dislikeReasonRecord.value = res?.data?.data || []
    })
    insertOnDone(({ data }) => {
        ElMessage({
            message: '提交成功！',
            type: 'success'
        })
    }) 
    updateOnDone(({ data }) => {
        ElMessage({
            message: '提交成功！',
            type: 'success'
        })
    }) 
    return {
        dislikeReasonRecord,
        insertDislikeReason,
        getDislikeReasonRecord,
        updateDislikeReason
    }
}