import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useLoginStore} from '@/stores/login'
import cookie from "js-cookie";
import {Cookies} from "@/utils/cookieHandler";
import { getItemWithTimestampCheck } from "@/common/commonJs";
const lang = cookie.get('ai_apps_lang') || (navigator.browserLanguage || navigator.language) || 'en'
const langes = JSON.parse(getItemWithTimestampCheck('current_langs_pack',7))[lang].tool
export function useAnswer(appUuid) {
    let answer = ref('')
    const loginStore = useLoginStore()
    const getAnswer = async (options, params, classInfo, spsPrompt) => {
        options.page.aiAnswer = langes['contGeneration']+'...'
        options.page.isAnswerFinished = true
        let arr = []
        options.page.ai.list.forEach(item => { 
            item.children.forEach(i => {
                arr.push(i.label)
            })
         })
        try {
           let url =  `${import.meta.env.VITE_DIFY_API_URL}/ai-base/v1/chat-messages`
            if (import.meta.env.MODE === 'development') {
                url = `${import.meta.env.VITE_DIFY_CONTEXT}/ai-base/v1/chat-messages`
            }
            // 添加 locale 参数到 URL
            const locale = cookie.get('ai_apps_lang') || localStorage.getItem('ai_apps_lang') || 'zh';
            url = url.includes("?") ? `${url}&locale=${locale}` : `${url}?locale=${locale}`;
           let  conversation_id = localStorage.getItem("conversation") ? JSON.parse(localStorage.getItem("conversation"))[appUuid+'_'+classInfo.app_id] : ''
           if(classInfo.app_id != classInfo.dAppUuid ){
            conversation_id = ""
           }
           const response = await fetch(url, {
                method: 'POST',
                headers:{
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${Cookies.get('yudaoToken')}`
                },
                body: JSON.stringify({
                    user: loginStore.userInfo?.userName,
                    response_mode: "streaming",
                    conversation_id:conversation_id,
                    query: params.query,
                    requestId:crypto.randomUUID(),
                    inputs: {
                        // spsPrompt: spsPrompt.value,
                        sourceText: classInfo.underlineText,
                        outputLanguage: getDefaultLanguage(),
                        sys: {
                            conversation_id: conversation_id,
                            files: [],
                            query: params.query,
                            user_id: ""
                        }
                    },
                    files: [],
                    appId: classInfo.app_id,
                    appUuid:appUuid
               })
           })
            if (!response.body) {
                throw new Error('Error');
            }
            answer.value = ''
            // 使用 ReadableStream 读取响应体
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let partialMessage = '';
            let errorMsg = '';
            outerLoop: while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }
                const textChunk = decoder.decode(value);
                const lines = (partialMessage + textChunk).split('\n\n');
                partialMessage = lines.pop() || '';
                if (Array.isArray(lines) && !lines.length && partialMessage && !partialMessage.startsWith('data:')) {
                    console.log('响应失败！')
                    options.page.isAnswerFinished = false
                    options.page.aiAnswer = `当前系统繁忙，请稍后重试～ `
                    return
                }
                for (let line of lines) {
                    var jsonString = line.replace('data:', '')
                    var data = JSON.parse(jsonString);
                    if(data.code == "5052") {
                      options.page.isAnswerFinished = false
                      options.page.aiAnswer = data.message
                      ElMessage.error(data.message)
                      return
                    }
                    if (data.error) {
                        errorMsg = data.error
                        break outerLoop;
                    }

                    classInfo.conversation_id = data.conversation_id
                    classInfo.message_id = data.message_id
                    classInfo.task_id = data.task_id
                    if (data.event === "message") {
                        answer.value += data.answer;
                        options.page.aiAnswer = answer.value;
                    }
                    if (data.event === "workflow_finished") {
                        options.page.isAnswerFinished = false
                    }
                    if (data.event === "message_end") {
                        options.page.isAnswerFinished = false
                    }
                        
                }
                if(classInfo.app_id != classInfo.dAppUuid){
                }else{
                    if(localStorage.getItem("conversation")&&JSON.parse(localStorage.getItem("conversation"))[appUuid+'_'+classInfo.app_id]){
                        classInfo.conversation_id = JSON.parse(localStorage.getItem("conversation"))[appUuid+'_'+classInfo.app_id]
                      }else{
                          let  conversation = JSON.parse(localStorage.getItem("conversation"))
                          conversation[appUuid+'_'+classInfo.app_id] = classInfo.conversation_id
                          localStorage.setItem('conversation',JSON.stringify(conversation))
                          classInfo.conversation_id = classInfo.conversation_id
                      }
                }
                
            }
            if (errorMsg) {
                options.page.isAnswerFinished = false
                options.page.aiAnswer = errorMsg
                ElMessage.error(errorMsg)
                return
            }
            options.page.aiAnswer = answer.value
        } catch (error) {
            options.page.isAnswerFinished = false
            options.page.aiAnswer = '当前系统繁忙，请稍后重试～ '
        }
       
    }
    return {
        getAnswer
    }
}

const getDefaultLanguage = () => {
  // 从 localStorage 中获取语言设置，如果没有则使用浏览器的语言设置
  const storedLang = cookie.get('ai_apps_lang');
  const browserLang = navigator.browserLanguage || navigator.language || navigator.userLanguage || 'en';
    const languages = {
        en: '英文',
        "zh": '中文',
        pt: '葡萄牙语',
        ar: '阿拉伯语',
        "tw": '繁体中文',
        id: '印尼语',
        ja: '日语',
        ko: '韩语',
        vi: '越南语',
        ms: '马来语',
        es: '西班牙语'
    };
  return languages[storedLang || browserLang || 'en'];
};