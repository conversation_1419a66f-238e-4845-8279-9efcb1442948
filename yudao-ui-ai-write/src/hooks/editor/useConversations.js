import ajax from "@/utils/request"
import { getConversationList } from '@/api/dify'
import { ref } from 'vue'
import { useEditorStore } from '@/stores/editor'  
export function useConversations() {
    let conversationList = ref([])
    const editorStore = useEditorStore()
    const fetchConversations = async (status) => {
        try {
            const response = await ajax.post(getConversationList,{
                appIds: editorStore.appIds,
                status: status,
                pageSize: 50,
                pageIndex: 1
            })
            conversationList.value = response?.data || []
            } catch (error) {
            console.error('Failed to fetch leading questions:', error)
        }
    } 
    return {
        conversationList,
        fetchConversations
    }
}