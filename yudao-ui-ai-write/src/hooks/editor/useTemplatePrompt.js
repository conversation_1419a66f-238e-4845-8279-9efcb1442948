// 获取单个实例语料

import ajax from "@/utils/request"
import { getTemplateAllPrompt } from '@/api/dify'
import { useRoute } from 'vue-router';
import { ref } from 'vue';
export function useTemplatePrompt() {
    const route = useRoute();
    let prompts = ref([])
    const fetchTemplatePrompt = async () => {
      alert(1)
        try {
            const response = await ajax.post(getTemplateAllPrompt, {
                name: route.params.name
            })
            prompts.value = response?.data
            } catch (error) {
            console.error('单个语料:', error)
        }
    } 
    return {
        prompts,
        fetchTemplatePrompt
    }
}