import { useQuery } from "@vue/apollo-composable"
import { getDictTypeGql, getTreeListGql } from '@/api/graphql'
import { usePrompts } from './usePrompts'
import { getToken } from '@/utils/auth'
import { ref } from "vue"
import { useEditorStore } from '@/stores/editor'
import { getItemWithTimestampCheck } from "@/common/commonJs";
import cookie from "js-cookie";
export function useType (dAppUuid) {
    const lang = cookie.get('ai_apps_lang') || (navigator.browserLanguage || navigator.language) || 'en'
    const langes = JSON.parse(getItemWithTimestampCheck('current_langs_pack',7))[lang].tool
    const  editorStore = useEditorStore()
    const { prompts,fetchPrompts } = usePrompts()
    let typeList = ref([])
    const { onResult: onResultType, refetch: refetchType} = useQuery(getDictTypeGql, {
        context: {
            authorization: `Bearer ${getToken('hasuraToken')}`, // 传递身份验证令牌
            'x-Hasura-Role': 'tenant_admin', // 传递用户ID
        },
    })
    const { refetch: refetchCategory } = useQuery(getTreeListGql)
    let dify_app_uuids = ref([])

    onResultType(async ({ data }) => {
        if (!data?.data || data.data.length === 0) return;
        const tempArr = data.data.map(item => {
            return {
                label: item.group_title,
                id: item.group_id,
                children: []
            }
        })

        const responses = await Promise.all(
            tempArr.map(item => refetchCategory({ path: `${item.id}-%` }).then(response => ({ response, id: item.id })))
        );
        responses.forEach(({ response }) => {
            const { data } = response;
            if (data?.data) {
                data.data.forEach(({ detail }) => {
                    if (detail.dify_app_uuid) {
                        dify_app_uuids.value.push(detail.dify_app_uuid);
                    }
                });
            }
        });
 
        // 获取prompts
        await fetchPrompts(dify_app_uuids.value);
        
        responses.forEach(({ response, id }) => {
            const { data } = response;
            if (data?.data) {
                const parent = tempArr.find(item => item.id === id);
                if (parent) {
                    data.data.forEach(({ detail }) => {
                        parent.children.push({
                            label: detail.prompt_name,
                            query: detail.prompt_name,
                            value: 'polish',
                            svg: 'polish',
                            dify_app_uuid: detail.dify_app_uuid,
                            prompt: prompts.value.find(item => item.dify_app_uuid === detail.dify_app_uuid)?.prompt || ''
                        });
                    });
                }
            }
        });
        let arr = []
        tempArr.forEach(item => { 
            item.children.forEach(i => {
                if(i.dify_app_uuid) {
                    arr.push(i.dify_app_uuid)
                }
            })
        })
        editorStore.appIds =[dAppUuid]
        tempArr.forEach((item)=>{
            item.label =  langes[item.label] 
            item.children.forEach((j)=>{
                j.label = langes[j.label] 
            })
        })
        typeList.value = tempArr;
        // if(lang.includes('zh')){
        //   typeList.value = tempArr;
        // }
        // if(lang.includes('en')){
        //   // 将tempArr数组中所有label对应的中文翻译成对应英文
        //   tempArr.forEach(item => {
        //     item.label = translationsEn[item.label] || item.label;
        //     item.children.forEach(i => {
        //       i.label = translationsEn[i.label] || i.label;
        //     })
        //   })
        //   typeList.value = tempArr;
        // }
        // if(lang.includes('fr')){
        //   tempArr.forEach(item => {
        //     item.label = translationsFr[item.label] || item.label;
        //     item.children.forEach(i => {
        //       i.label = translationsFr[i.label] || i.label;
        //     })
        //   })
        //   typeList.value = tempArr;
        // }
        
    })

    return {
        typeList,
        refetchType
    }
}
// const translationsEn = {
//   '写作辅助': 'Writing Assistance',
//   '润色': 'Polish',
//   '扩写': 'Expand',
//   '简写': 'Abbreviate',
//   '续写': 'Continue Writing',
//   '翻译': 'Translation',
//   '中译英': 'Chinese to English',
//   '英译中': 'English to Chinese',
//   '文献': 'Literature',
//   '查找文献出处': 'Find Literature Source',
//   '相关文献推荐': 'Related Literature Recommendations',
//   '发现新知识': 'Discover New Knowledge',
//   '指南检索': 'Guideline Search',
//   '主站资源检索': 'Main Site Resource Search'
// };
// const translationsFr = {
//   '写作辅助': 'Écriture d\'aide',
//   '润色': 'Polir',
//   '扩写': 'Étendre',
//   '简写': 'Abréger',
//   '续写': 'Continuer à écrire',
//   '翻译': 'Traduction',
//   '中译英': 'Chinois vers anglais',
//   '英译中': 'Anglais vers chinois',
//   '文献': 'Littérature',
//   '查找文献出处': 'Trouver la source de la littérature',
//   '相关文献推荐': 'Recommandations de littérature connexe',
//   '发现新知识': 'Découvrir de nouvelles connaissances',
//   '指南检索': 'Recherche de directives',
//   '主站资源检索': 'Recherche de ressources sur le site principal'
// }