// 会话收藏历史记录
import { useQuery } from "@vue/apollo-composable"
import { getCollectionGql } from '@/api/graphql'
import { ref, computed } from "vue"
export function useCollectionList() {
    const collectionList = ref([])
    const { refetch: refetchCollection, onResult } = useQuery(getCollectionGql)
    onResult(res => {
        collectionList.value = res.data?.data || []
    })
    return {
        collectionList,
        refetchCollection,
    }
}