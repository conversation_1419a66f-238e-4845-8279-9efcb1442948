import { useEditorStore } from '@/stores/editor'  
import { useQuery } from "@vue/apollo-composable"
import { getMessagesGql } from '@/api/graphql'
import { ref, watch } from "vue"
export function useMessages() {
    let messagesList = ref([])
    const { onResult, refetch:refetchMessages } = useQuery(getMessagesGql)
    
    onResult(({ data }) => {
        if (!data?.data || data.data.length === 0) return;
        messagesList.value = data.data.map(item => {
            return {
                ...item,
                message_feedback: {
                    rating: item.message_feedback?.rating || null
                }
            }
        })
    })

    return {
        refetchMessages,
        messagesList
    }
}