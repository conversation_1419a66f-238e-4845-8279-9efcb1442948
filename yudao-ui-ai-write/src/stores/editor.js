import { defineStore } from 'pinia'
export const useEditorStore = defineStore('login', {
    state: () => {
        return {
            categoryDifyIds: [],
            conversation_id: '',
            task_id: '',
            message_id: '',
            dislike_reasons: [
                '格式问题',
                '逻辑问题',
                '有害信息',
                '事实错误',
                '没有帮助',
                '答非所问'
            ],
            appIds: [],
            appId: ''
        }
  }
})