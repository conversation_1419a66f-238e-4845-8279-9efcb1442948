import { defineStore } from 'pinia'
import ajax from "@/utils/request"
import { MAINLOGIN } from "@/api/login"
import { getToken, setToken, removeToken } from "@/utils/auth"
import cookie from "js-cookie";
import {Cookies} from "@/utils/cookieHandler"
export const useLoginStore = defineStore('login', {
  state: () => ({
        token: Cookies.get('yudaoToken') || null,
        hasuraToken: getToken('hasuraToken') || null,
        userInfo:  JSON.parse(cookie.get("userInfo") || '{}'),
  }),
    actions: {
        resetToken () {
            cookie.remove("yudaoToken", { domain: ".medsci.cn" });
            cookie.remove("yudaoToken", { domain: ".medon.com.cn" });
            cookie.remove("yudaoToken", { domain: "localhost" });
            removeToken('hasuraToken')
            this.token = ''
            this.hasuraToken = ''
        },
        async loginMain () {
            if(this.hasuraToken && this.token) return
            if (cookie.get("userInfo")) {
                const userInfo = JSON.parse(cookie.get("userInfo"))
                try {
                    const res = await ajax.post(MAINLOGIN, {
                        userId: userInfo.userId,
                        userName: userInfo.userName,
                        realName: userInfo.realName,
                        avatar: userInfo.avatar,
                        plaintextUserId: userInfo.plaintextUserId,
                        mobile: userInfo.mobile,
                    })
                    Cookies.set('yudaoToken', res.data.token)
                    setToken('hasuraToken', res.data.htoken)
                    this.token = res.data.token
                    this.hasuraToken = res.data.htoken
                } catch (error) {
                    console.error("登录失败:", error);
                    // 可以添加更详细的错误信息展示给用户
                }
            }
        },
  },
   getters: {  
       isLoggedIn: (state) => {
           return !!state.hasuraToken
        },
  },  
})