// 导出语言映射
import cookie from "js-cookie";
export const languages = {
  en: '英文',
  "zh": '中文',
  pt: '葡萄牙语',
  ar: '阿拉伯语',
  "tw": '繁体中文',
  id: '印尼语',
  ja: '日语',
  ko: '韩语',
  vi: '越南语',
  ms: '马来语',
  es: '西班牙语'
};

// 导出应用类型
export const appTypes = {
  工具: 'market.tool',
  写作: 'market.write',
  问答: 'market.chat',
  科研工具: 'market.researchTool',                        
  学术写作: 'market.academicWrite',
  医学会话: 'market.medicalChat',
  全部: 'market.all',
  我的应用: 'market.myApp',
};
// 获取默认语言代码
export const getDefaultLanguageCode = () => {
  // 从 localStorage 中获取语言设置，如果没有则使用浏览器的语言设置
  const storedLang = cookie.get('ai_apps_lang');
  const browserLang = (navigator.browserLanguage || navigator.language || navigator.userLanguage || 'en');
  
  return storedLang || browserLang || 'en';
};

// 导出默认语言代码
export const defaultLanguage = () => {
  return getDefaultLanguageCode();
};

// 导出默认语言名称
export const defaultLanguageName = () => {
  const langCode = getDefaultLanguageCode();
  return languages[langCode] || languages['en']; // 提供一个默认值以防未定义
};

// 格式化后台语言包配置
export const transformArrayToObject = (array) => {
  const result = {};
 
  array.forEach(item => {
    // 假设 key 字段的格式总是 "xx.yy"，其中 "xx" 是语言代码，"yy" 是具体的键
    const [langCode] = item.key.split('.');
    const value = JSON.parse(item.value);
 
    // 如果语言代码对应的对象还不存在，则创建一个空对象
    if (!result[langCode]) {
      result[langCode] = {};
    }

    // 将具体的键值对添加到语言代码对应的对象中
    result[langCode] = {...result[langCode], ...value}; // 解析 value 字段的 JSON 字符串
  });
 
  return result;
}

// 设置一个键并存储当前的时间戳
export const setItemWithTimestamp = (key, value) => {
  const timestamp = Date.now(); // 获取当前时间的时间戳
  localStorage.setItem(key + '_value', value); // 存储实际的值
  localStorage.setItem(key + '_timestamp', timestamp); // 存储时间戳
}

// 获取带有时间戳的键的值，并检查是否超过了指定的天数（以毫秒为单位）
export const getItemWithTimestampCheck = (key, daysThreshold) => {
  const valueKey = key + '_value';
  const timestampKey = key + '_timestamp';

  // 获取存储的值和时间戳
  const storedValue = localStorage.getItem(valueKey);
  const storedTimestamp = localStorage.getItem(timestampKey);

  if (storedValue !== null && storedTimestamp !== null) {
      // 将存储的时间戳转换为日期对象
      const storedDate = new Date(storedTimestamp);
      // 获取当前时间
      const currentDate = new Date();
      // 计算时间差（毫秒）
      const timeDifference = currentDate - storedDate;
      // 将时间差转换为天数
      const differenceInDays = timeDifference / (1000 * 60 * 60 * 24);

      // 检查是否超过了指定的天数阈值
      if (differenceInDays > daysThreshold) {
          // 如果超过了，可以选择删除键或返回null/false等
          localStorage.removeItem(valueKey);
          localStorage.removeItem(timestampKey);
          return null; // 或返回false，表示数据已过期
      } else {
          // 返回存储的值
          return storedValue;
      }
  } else {
      // 如果键或时间戳不存在，返回null/false等
      return null; // 或返回false
  }
}
