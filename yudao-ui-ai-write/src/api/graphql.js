import gql from "graphql-tag";

/*
    获取具体的字典分类下的字典词
    参数：
*/
export const getTreeListGql = gql`
  query getTreeListGql($path: String!, $status: String = "正常") {
    data: ai_base_ai_common_relations(where: {path: {_like: $path}, status: {_eq: $status}, type: {_in: ["字典分类-字典词","字典词-字典词"]}}) {
      relate_id
      entity_id
      path
      id
      status
      detail: dict_prompt {
        id
        prompt_name
        prompt_description
        prompt_category
        prompt_status
        dify_app_uuid
        directory_md
      }
    }
  }
`;

// 获取翻译/写作/文献
export const getDictTypeGql = gql`
    query getDictType($_in: [Int!] = [55, 56, 57]) {
        data: ai_base_ai_groups(where: {group_id: {_in: $_in}, group_status: {_eq: "启用"}}, order_by: {group_id: asc}) {
            group_id
            group_status
            group_title
        }
    }
`

// 历史会话
export const getConversationsGql = gql`
    query conversations($app_id: [uuid!]) {
        data: conversations(where: {app_id: {_in: $app_id}}, order_by: {created_at: desc}, limit: 50) {
            app_id
            name
            id
            collection: conversation_collection{
                conversation_id
                status
            }
        }
    }
`

// 历史会话log
export const getMessagesGql = gql`
    query getMessage($conversation_id: uuid!) {
        data: messages(where: {conversation_id: {_eq: $conversation_id}}, order_by: {created_at: asc}) {
            app_id
            id
            conversation_id
            query
            answer
            message_feedback {
                rating
            }
        }
    }   
`

// 收藏会话
export const insertCollectionGql = gql`
    mutation insertCollection($conversation_id: uuid!, $from_end_user: String = "") {
        data: insert_collections(
            objects: {
                conversation_id: $conversation_id,
                from_end_user: $from_end_user,
                status: "正常"
            }
        ) {
            returning {
                app_id
                conversation_id
                created_at
                from_end_user
                id
                status
            }
        }
    }
`
// 取消收藏
export const updateCollectionGql = gql`
    mutation updateCollection($conversation_id: uuid!, $status: String!) {
        data: update_collections(
            where: {
                conversation_id: {_eq: $conversation_id},
            },
             _set: {status: $status}
        ) {
            returning {
                app_id
                conversation_id
                from_end_user
                id
                status
            }
        }
    }
`

// 查询单个会话收藏记录
export const getCollectionGql = gql`
    query getCollection($conversation_id: uuid!) {
        data: collections(where: {conversation_id: {_eq: $conversation_id}}) {
            conversation_id
            app_id
            from_end_user
            status
        }
    }
`

// 提交不喜欢原因
export const insertDislikeReasonGql = gql`
    mutation insertDislikeReason($ext: String, $message_id: uuid!, $reason: String) {
        data: insert_message_feedbacks_dislike_one(object: {ext: $ext, message_id: $message_id, reason: $reason}) {
            created_at
            ext
            id
            message_id
            reason
            updated_at
        }
    }
`

// 获取message是否有提交过吐槽原因
export const getDislikeReasonRecordGql = gql`
    query dislikeReasonRecord($message_id: uuid!) {
        data: message_feedbacks_dislike(where: {message_id: {_eq: $message_id}}) {
            updated_at
            reason
            message_id
            id
            ext
            created_at
        }
    }
`
// update不喜欢原因
export const updateDislikeReasonGql = gql`
    mutation updateDislikeReason($message_id: uuid!, $reason: String, $ext: String) {
        data: update_message_feedbacks_dislike(where: {message_id: {message_id: $_eq}}, _set: {reason: $reason, ext: $ext}) {
            returning {
            created_at
            ext
            id
            message_id
            reason
            updated_at
            }
        }
    }
`

// 根据应用ID获取应用详情
export const getAppDetail = gql`
query getAppDetail($id: Int!) {
  data:ai_base_ai_app_langs_by_pk(id: $id) {
    id
    pid
    directory_md
    dify_app_uuid
    app_name
    app_status
    app_type
  }
}
`
// 根据应用uuid获取应用详情
export const getAppDetailByUuid = gql`
query getAppDetailByUuid($app_uuid: uuid!) {
  data: ai_base_ai_app_langs(where: {app_uuid: {_eq: $app_uuid}}) {
    id
    pid
    directory_md
    dify_app_uuid
    app_name
    app_status
    app_type
  }
}
`