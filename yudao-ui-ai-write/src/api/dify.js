const ctx = import.meta.env.VITE_DIFY_CONTEXT || '';


// 流模式，创建会话消息
export const chatMsgStream = `${ctx}/ai-base/v1/chat-messages` // ai会话
          
// export const getLeadingQuestions = `${ctx}/ai-base/v1/parameters` 

export const feedback = `${ctx}/ai-base/v1/messages/feedbacks` // 点赞/反对

export const getAppPrompt = `${ctx}/ai-base/v1/appParams` // 获取单个实例语料

export const getAppPrompts = `${ctx}/ai-base/index/appsParams` // 获取多个语料

export const getConversationList = `${ctx}/ai-base/v1/myCollection` // 获取会话

export const stopChat = `${ctx}/ai-base/v1/chat-messages/stop` // 停止会话

export const getLeadingQuestions = `${ctx}/ai-base/v1/workflows/run1` // 获取引导提问

export const getTemplateAllPrompt = `${ctx}/ai-base/v1/getAppsByName` // 根据模板名称获取该模板下所有目录的实例

export const getConfigPage = `${ctx}/ai-base/index/getConfigPage` // 根据模板名称获取该模板下所有目录的实例

// 获取访问者国家
export const getLocation = `${ctx}/ai-base/index/getLocation` // 根据模板名称获取该模板下所有目录的实例

// 根据uuid查询应用
export const getAppByUuid = `${ctx}/ai-base/index/getAppByUuid`

// 获取grafphql token
export const getHToken = `${ctx}/ai-base/index/getHToken`

// 获取当前应用的详情
export const getAppList = `${ctx}/ai-base/index/getAppList`

// 获取当前应用的详情
export const createSubscription = `${ctx}/ai-base/appUser/createSubscription`
// 获取当前应用的详情
export const getSubOrder = `${ctx}/ai-base/index/getSubOrder`
// 获取当前应用使用次数
export const freeLimit = `${ctx}/ai-base/index/free-limit`
// 取消订阅
export const cancelSubscription = `${ctx}/ai-base/appUser/cancelSubscription?appUuid=`

// 获取配置的试用次数
export const getPackageByKey = `${ctx}/ai-base/index/getPackageByKey`