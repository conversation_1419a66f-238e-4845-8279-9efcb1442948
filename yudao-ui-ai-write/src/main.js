import { createApp, nextTick } from 'vue'
import { createPinia } from 'pinia'
import 'normalize.css'
import "@/assets/main.css"
import "@/assets/common.css"
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'vant/lib/index.css';
// import 'virtual:svg-icons-register'
// 注册全局组件
import gloablComponent from '@/components/index';
// SEO优化
import { createHead } from '@vueuse/head';
import {  getHToken } from "@/api/dify";
// markdown
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github';
import '@kangc/v-md-editor/lib/theme/style/github.css';
import hljs from 'highlight.js';
import ajax from "@/utils/request";
// 国际化
import createI18nInstance from '@/utils/i18n.js';
// 创建head实例
const head = createHead();

async function bootstrap() {
   if(!localStorage.getItem("hasuraToken")){
      const hasuraToken = await ajax.get(getHToken)
      localStorage.setItem("hasuraToken",hasuraToken.data.htoken)
    }
const i18n =await createI18nInstance();
// document.title = i18n.global.t('梅斯小智')
VMdPreview.use(githubTheme, {
   Hljs: hljs,
});
createApp(App)
.use(head)
   .use(createPinia())
  .use(router)
  .use(ElementPlus)
  .use(i18n)
  .use(gloablComponent)
  .use(VMdPreview)
  .mount('#app')
}
bootstrap()
// apolloClient
import { provideApolloClient } from '@vue/apollo-composable';  
import apolloClient from '@/utils/apollo';
provideApolloClient(apolloClient);