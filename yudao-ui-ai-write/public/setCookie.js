function transformArrayToObject(array) {
    const result = {};

    array.forEach(item => {
        // 假设 key 字段的格式总是 "xx.yy"，其中 "xx" 是语言代码，"yy" 是具体的键
        const [langCode] = item.key.split('.');
        const value = JSON.parse(item.value);

        // 如果语言代码对应的对象还不存在，则创建一个空对象
        if (!result[langCode]) {
            result[langCode] = {};
        }

        // 将具体的键值对添加到语言代码对应的对象中
        result[langCode] = { ...result[langCode], ...value }; // 解析 value 字段的 JSON 字符串
    });

    return result;
}

// 设置一个键并存储当前的时间戳
function setItemWithTimestamp(key, value) {
    const timestamp = Date.now(); // 获取当前时间的时间戳
    localStorage.setItem(key + '_value', value); // 存储实际的值
    localStorage.setItem(key + '_timestamp', timestamp); // 存储时间戳
}

// 获取带有时间戳的键的值，并检查是否超过了指定的天数（以毫秒为单位）
function getItemWithTimestampCheck(key, daysThreshold) {
    const valueKey = key + '_value';
    const timestampKey = key + '_timestamp';

    // 获取存储的值和时间戳
    const storedValue = localStorage.getItem(valueKey);
    const storedTimestamp = localStorage.getItem(timestampKey);

    if (storedValue !== null && storedTimestamp !== null) {
        // 将存储的时间戳转换为日期对象
        const storedDate = new Date(storedTimestamp);
        // 获取当前时间
        const currentDate = new Date();
        // 计算时间差（毫秒）
        const timeDifference = currentDate - storedDate;
        // 将时间差转换为天数
        const differenceInDays = timeDifference / (1000 * 60 * 60 * 24);

        // 检查是否超过了指定的天数阈值
        if (differenceInDays > daysThreshold) {
            // 如果超过了，可以选择删除键或返回null/false等
            localStorage.removeItem(valueKey);
            localStorage.removeItem(timestampKey);
            return null; // 或返回false，表示数据已过期
        } else {
            // 返回存储的值
            return storedValue;
        }
    } else {
        // 如果键或时间戳不存在，返回null/false等
        return null; // 或返回false
    }
}
// 定义一个函数来调用接口
function fetchLanguageConfig() {
    let langsStr = getItemWithTimestampCheck('current_langs_pack', 7)
    let umoData = getItemWithTimestampCheck('current_langs_pack_umo', 7)
    if (!langsStr || !umoData) {
        // 接口的 URL
        const apiUrl = location.origin.includes('medsci.cn')?location.origin+'/dev-api/ai-base/index/getConfigPage':'https://ai-base.medon.com.cn/dev-api/ai-base/index/getConfigPage';

        // 使用 fetch API 调用接口
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.data.list.length !== 0) {
                    langsStr = JSON.stringify(transformArrayToObject(data.data.list))
                    setItemWithTimestamp('current_langs_pack', langsStr);
                    let filteredData = data.data.list.filter(e => e.value != "{}" && e.key.includes(".dify"));
                    let umoData = filteredData.reduce((acc, item) => {
                        // 如果对象中还没有这个键，初始化一个空数组
                        if (!acc[item.key.substr(0, item.key.indexOf('.'))]) {
                            acc[item.key.substr(0, item.key.indexOf('.'))] = {};
                        }
                        // 将当前项推入对应键的数组
                        acc[item.key.substr(0, item.key.indexOf('.'))] = (JSON.parse(item.value));
                        return acc;
                    }, {});

                    setItemWithTimestamp('current_langs_pack_umo', JSON.stringify(umoData));
                }
            })
            .catch(error => {
                console.error('Failed to fetch language config:', error);
            });
    }
}
setInterval(() => {
    fetchLanguageConfig()
}, 1000);