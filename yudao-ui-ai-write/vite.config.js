import { fileURLToPath, URL } from 'node:url'

import { defineConfig,loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    // 加载基于当前模式的环境变量
  const env = loadEnv(mode, process.cwd());
  return {
    plugins: [
      vue(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    base: '/ai-write/',
    build: {
      outDir: '../ai-write-public/ai-write', // 将输出目录设置为 'build'
      assetsDir: 'static', // 静态资源目录设置为 'static'
      sourcemap: false // 生产环境不生成 source map
    },
    server: {
      port: 3001,//启动端口
      open: false,
      // 反向代理
      proxy: {
        [env.VITE_DIFY_CONTEXT]: {
          target: env.VITE_DIFY_API_URL,
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp(`^${env.VITE_DIFY_CONTEXT}`), '')
        },
        [env.VITE_YUDAO_CONTEXT]: {
          target: env.VITE_YUDAO_API_URL,
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp(`^${env.VITE_YUDAO_CONTEXT}`), '')
        },
        [env.VITE_CHAT_CONTEXT]: {
          target: env.VITE_CHAT_API_URL,
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp(`^${env.VITE_CHAT_CONTEXT}`), '')
        }
      }
    },
  }
})
