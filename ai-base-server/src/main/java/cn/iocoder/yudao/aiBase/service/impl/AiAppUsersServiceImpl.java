package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.AppUserParam;
import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import cn.iocoder.yudao.aiBase.mapper.AiAppUsersMapper;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiAppUsersServiceImpl extends ServiceImpl<AiAppUsersMapper, AiAppUsers> implements AiAppUsersService {

    @Autowired
    private AiAppUsageRecordsService aiAppUsageRecordsService;

    @Override
    public List<AiAppUsers> selectList(AppUserParam reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public PageResult<AiAppUsers> selectPage(AppUserParam reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public Map<String, AiAppUsers> selectMap(Long socialUserId, Integer socialType) {
        AppUserParam reqVO = AppUserParam.builder().socialUserId(socialUserId).socialType(socialType).build();
        List<AiAppUsers> appUsers = selectList(reqVO);
        return appUsers.stream().collect(Collectors.toMap(AiAppUsers::getAppUuid, item -> item, (k1, k2)->k1));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTask(LocalDateTime end) {
        AppUserParam reqVO = AppUserParam.builder().status(BaseConstant.ONE).expiredAt(new LocalDateTime[]{end, end.plusDays(BaseConstant.ONE)}).build();
        List<AiAppUsers> list = selectList(reqVO);
        list.forEach(user -> {
            user.setStatus(BaseConstant.TWO);
            user.setUpdatedAt(LocalDateTime.now());
            updateById(user);
        });
    }

    /**
     * 获取用户app
     * @param socialUserId
     * @param socialType
     * @param appUuid
     * @return
     */
    @Override
    public AiAppUsers getAppUser(Long socialUserId, Integer socialType, String appUuid) {
        AppUserParam reqVO = AppUserParam.builder().socialUserId(socialUserId).socialType(socialType).appUuid(appUuid).build();
        List<AiAppUsers> list = selectList(reqVO);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public AiAppUsers getAppUser(String appUuid) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        return getAppUser(loginUser.getId(), loginUser.getUserType(), appUuid);
    }

    /**
     * 获取用户app或者创建
     * @param socialUserId
     * @param socialType
     * @param appUuid
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiAppUsers getOrCreateAppUser(Long socialUserId, Integer socialType, String stripeCustomerId, String appUuid) {
        AiAppUsers appUser = getAppUser(socialUserId, socialType, appUuid);
        if (appUser == null) {
            appUser = new AiAppUsers();
            appUser.setSocialUserId(socialUserId);
            appUser.setSocialType(socialType);
            appUser.setStripeCustomerId(stripeCustomerId);
            appUser.setAppUuid(appUuid);
            appUser.setStatus(BaseConstant.TWO);
            appUser.setCreatedAt(LocalDateTime.now());
            baseMapper.insert(appUser);
        }
        return appUser;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiAppUsers getOrCreateAppUser(String appUuid) {
        AiAppUsers appUser = getAppUser(appUuid);
        if (appUser == null) {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            appUser = new AiAppUsers();
            appUser.setSocialUserId(loginUser.getId());
            appUser.setSocialType(loginUser.getUserType());
            appUser.setAppUuid(appUuid);
            appUser.setStatus(BaseConstant.TWO);
            appUser.setStartAt(LocalDateTime.now());
            appUser.setCreatedAt(LocalDateTime.now());
            baseMapper.insert(appUser);
        }
        return appUser;
    }

    /**
     * 支付成功后更新订阅状态
     * @param appUuid
     * @param monthNum
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiAppUsers updateSub(String appUuid, Integer monthNum) {
        AiAppUsers appUser = getOrCreateAppUser(appUuid);
        LocalDateTime now = LocalDateTime.now();
        if (appUser.getFirstBuyTime() == null) {
            appUser.setFirstBuyTime(now);
        }
        if (appUser.getStartAt() == null) {
            // 首次支付账单
            appUser.setStartAt(now);
        } else {
            // 再次支付账单
            if (now.isAfter(appUser.getExpireAt())) {
                // 当前已过期
                appUser.setStartAt(now);
            } else {
                // 当前未过期
                appUser.setStartAt(appUser.getExpireAt());
            }
        }

        appUser.setExpireAt(appUser.getStartAt().plusMonths(monthNum));
        appUser.setUpdatedAt(now);
        appUser.setStatus(BaseConstant.ONE);
        baseMapper.updateById(appUser);

        return appUser;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiAppUsers updateSub(String appUuid, LocalDateTime startAt, LocalDateTime expireAt) {
        AiAppUsers appUser = getOrCreateAppUser(appUuid);
        LocalDateTime now = LocalDateTime.now();
        if (appUser.getFirstBuyTime() == null) {
            appUser.setFirstBuyTime(now);
        }
        appUser.setStartAt(startAt);
        appUser.setExpireAt(expireAt);
        appUser.setUpdatedAt(now);
        appUser.setStatus(BaseConstant.ONE);
        baseMapper.updateById(appUser);

        return appUser;
    }

    @Override
    public int countAll(String appUuid) {
        return aiAppUsageRecordsService.countAll(appUuid);
    }

}
