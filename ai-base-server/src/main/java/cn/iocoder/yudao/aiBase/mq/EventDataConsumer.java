package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.aiBase.service.AiAppLangsService;
import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EventDataConsumer extends AbstractRedisStreamMessageListener<EventDataMsg1> {

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Override
    public void onMessage(EventDataMsg1 msg) {
        String consumerInfo = String.format("[消费者:%s][组:%s][Stream:%s]",
                getConsumerName(), getGroup(), getStreamKey());
        log.info("{}开始处理消息EventDataMsg{}", consumerInfo, msg);

        long startTime = System.currentTimeMillis();
        try {
            aiAppLangsService.webhook(msg.getSigHeader(), msg.getPayload());

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("{}处理消息成功，耗时:{}ms", consumerInfo, processingTime);
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("{}处理消息失败，耗时:{}ms", consumerInfo, processingTime, e);
            throw e; // 重新抛出异常，让框架处理重试逻辑
        }
    }

    private String getConsumerName() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName() + "-" +
                   java.lang.management.ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        } catch (Exception e) {
            return "unknown-consumer";
        }
    }
}
